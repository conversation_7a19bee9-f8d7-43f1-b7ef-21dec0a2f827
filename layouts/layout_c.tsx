import { Box, Grid, GridItem, VStack } from '@chakra-ui/react'
import { SliderCard } from '@components/home/<USER>'
import MotionBox from '@components/motion-box'
import Photo from '@components/photo'
import { ExtendedHomeLayout } from '@data'
import React, { FC } from 'react'

const LayoutC: FC<{
  slide: ExtendedHomeLayout
}> = ({ slide }) => {
  return (
    <Grid
      templateColumns={['repeat(4, 1fr)', null, null, 'repeat(12, 1fr)']}
      gap={4}
      height={['calc(30vh + 16px)', '100vh']}
      py={[0, 'calc(100vh/12)']}
      px={[5, null, null, 8]}
      mb={[14, 0]}
    >
      <GridItem colStart={[1, null, null, 2]} colSpan={[2, null, null, 5]}>
        <VStack spacing={4} h="100%">
          {slide.gallery.length > 0 && (
            <Box pos="relative" w="100%" h={['15vh', '100%']}>
              <SliderCard
                image={slide.gallery[0].featuredImage}
                title={slide.gallery[0].title ?? ''}
                categories={slide.gallery[0].categories ?? []}
                slug={slide.gallery[0].slug?.current ?? ''}
                hoverColor={slide.gallery[0].themeColor?.hex ?? '#E6B976'}
              />
            </Box>
          )}
          {slide.gallery.length > 1 && (
            <Box pos="relative" w="100%" h={['15vh', '100%']}>
              <SliderCard
                image={slide.gallery[1].featuredImage}
                title={slide.gallery[1].title ?? ''}
                categories={slide.gallery[1].categories ?? []}
                slug={slide.gallery[1].slug?.current ?? ''}
                hoverColor={slide.gallery[1].themeColor?.hex ?? '#E6B976'}
              />
            </Box>
          )}
        </VStack>
      </GridItem>
      {slide.gallery.length > 2 && (
        <GridItem colStart={[3, null, null, 7]} colSpan={[2, null, null, 5]}>
          <SliderCard
            image={slide.gallery[2].featuredImage}
            title={slide.gallery[2].title ?? ''}
            categories={slide.gallery[2].categories ?? []}
            slug={slide.gallery[2].slug?.current ?? ''}
            hoverColor={slide.gallery[2].themeColor?.hex ?? '#E6B976'}
          />
        </GridItem>
      )}
    </Grid>
  )
}

export default LayoutC
