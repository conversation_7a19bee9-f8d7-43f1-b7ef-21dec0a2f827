import { Box, Grid, GridItem } from '@chakra-ui/react'
import MotionBox from '@components/motion-box'
import { enterExitAnim } from 'data/animation'
import React from 'react'

const AboutTransition = () => {
  return (
    <MotionBox
      key="about-transition"
      pos="fixed"
      inset={0}
      h="var(--chakra-vh)"
      w="100vw"
      zIndex={95}
      variants={{ ...enterExitAnim }}
      initial="initial"
      animate="enter"
      exit="exit"
    >
      <Grid templateColumns="repeat(12, 1fr)" autoRows="calc(100vh/12)">
        <GridItem colStart={1} colEnd={13} rowStart={1} rowEnd={13}>
          <Box bg="#FF9865" h="100%" />
        </GridItem>
        <GridItem colStart={1} colEnd={10} rowStart={1} rowEnd={10}>
          <Box bg="#BFB200" h="100%" />
        </GridItem>
        <GridItem colStart={1} colEnd={7} rowStart={1} rowEnd={7}>
          <Box bg="#4B25FF" h="100%" />
        </GridItem>
      </Grid>
    </MotionBox>
  )
}

export default AboutTransition
