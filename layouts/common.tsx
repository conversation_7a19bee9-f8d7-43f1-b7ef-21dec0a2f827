import React from 'react'
import Header from '@components/header'
import { Chakra } from '@lib/chakra'
import { Box } from '@chakra-ui/react'

type Props = {
  cookies?: string
  theme?: string
  children?: React.ReactNode
  bg?: string[] | string
}

const CommonLayout = ({ cookies, children, theme, bg }: Props) => {
  return (
    <Chakra cookies={cookies}>
      <Box bg={bg} minH='var(--chakra-vh)'>
        <Header theme={theme} />
        {children}
      </Box>
    </Chakra>
  )
}

export default CommonLayout
