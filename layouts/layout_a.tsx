import { Box, Grid, GridItem, Text } from '@chakra-ui/react'
import { SliderCard } from '@components/home/<USER>'
import MotionBox from '@components/motion-box'
import Photo, { ExtendedSanityImageObject } from '@components/photo'
import { ExtendedHomeLayout, Project } from '@data'
import React, { FC } from 'react'

const LayoutA: FC<{
  slide: ExtendedHomeLayout
}> = ({ slide }) => {
  return (
    <Grid
      templateColumns={['repeat(4, 1fr)', null, null, 'repeat(12, 1fr)']}
      gap={4}
      height={['calc(30vh)', '100vh']}
      py={[0, 'calc(100vh/12)']}
      px={[5, null, null, 8]}
      mb={[14, 0]}
    >
      {slide.gallery.length > 0 && (
        <GridItem colStart={[1, null, null, 2]} colSpan={[2, null, null, 5]}>
          <SliderCard
            image={slide.gallery[0].featuredImage}
            title={slide.gallery[0].title ?? ''}
            categories={slide.gallery[0].categories ?? []}
            slug={slide.gallery[0].slug?.current ?? ''}
            hoverColor={slide.gallery[0].themeColor?.hex ?? '#E6B976'}
          />
        </GridItem>
      )}
      {slide.gallery.length > 1 && (
        <GridItem colStart={[3, null, null, 7]} colSpan={[2, null, null, 5]}>
          <SliderCard
            image={slide.gallery[1].featuredImage}
            title={slide.gallery[1].title ?? ''}
            categories={slide.gallery[1].categories ?? []}
            slug={slide.gallery[1].slug?.current ?? ''}
            hoverColor={slide.gallery[1].themeColor?.hex ?? '#E6B976'}
          />
        </GridItem>
      )}
    </Grid>
  )
}

export default LayoutA
