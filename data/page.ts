import { proxy, subscribe } from 'valtio'
import _ from 'lodash'

export interface PageLink {
  page?: string
}

interface PageStore {
  pageVisited: PageLink[]
}

// TODO: hydration bug
let defaultStore: PageStore = { pageVisited: [] }
// if (typeof window != 'undefined') {
//   if (window.localStorage) {
//     if (localStorage.getItem('lightbox') != null) {
//       let result = JSON.parse(localStorage.getItem('lightbox')!)
//       if (!_.isEmpty(result)) {
//         console.log(result)
//         defaultStore = result
//       }
//     }
//   }
// }

const pageStore = proxy<PageStore>(defaultStore)
subscribe(pageStore, () => {
  localStorage.setItem('page', JSON.stringify(pageStore))
})

export default pageStore
