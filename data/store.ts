import { proxy, subscribe } from 'valtio'
import { ExtendedSanityImageObject } from '@components/photo'
import _ from 'lodash'

export interface Category {
  name?: string
}

export interface LightboxImage extends ExtendedSanityImageObject {
  title?: string
  project: string
  categories?: Category[]
  workType?: string
  monthYear?: string
}

interface LightboxStore {
  mediaSelected: LightboxImage[] // store image object?
}

// TODO: hydration bug
let defaultStore: LightboxStore = { mediaSelected: [] }
// if (typeof window != 'undefined') {
//   if (window.localStorage) {
//     if (localStorage.getItem('lightbox') != null) {
//       let result = JSON.parse(localStorage.getItem('lightbox')!)
//       if (!_.isEmpty(result)) {
//         console.log(result)
//         defaultStore = result
//       }
//     }
//   }
// }

const lightboxStore = proxy<LightboxStore>(defaultStore)

export const checkLightbox = async () => {
  const lightbox = getLightbox()

  if (lightbox) {
    lightboxStore.mediaSelected = lightbox.mediaSelected
  } else {
  }
}

export function getLightbox() {
  try {
    const lightbox = localStorage.getItem('lightbox')
    if (lightbox) {
      return JSON.parse(lightbox)
    }
  } catch (err) {
    clearLightbox()
  }

  return null
}

export function clearLightbox() {
  localStorage.removeItem('lightbox')
}

subscribe(lightboxStore, () => {
  localStorage.setItem('lightbox', JSON.stringify(lightboxStore))
})

export default lightboxStore
