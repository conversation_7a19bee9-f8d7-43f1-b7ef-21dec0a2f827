import {
  ExtendedSanityImageObject,
  GalleryItem,
} from '@components/photo'
import { typedSanityClient, rawSanityClient } from '@lib/sanity'
import type * as Schema from '../studio/schema'
import { WorkCategory } from '../studio/schema'

// Fetch all dynamic docs
// export async function getAllDocSlugs(doc: string) {
//   const data = await typedSanityClient.getAll(
//     'page',
//     `_type == "${doc}" && wasDeleted != true && isDraft != true`
//   )

//   return data
// }

export async function getAllDocSlugs(
  doc: 'page' | 'work',
  filter: string | null = null
) {
  const data = await typedSanityClient.getAll(
    doc,
    filter ?? `wasDeleted != true && isDraft != true`
  )

  // const rawData = await rawSanityClient.fetch<Schema.Page>(
  //   `*[_type == "${doc}" && wasDeleted != true && isDraft != true]{ slug }`
  // )

  return data.map((item) => ({ slug: item.slug?.current, id: item._id }))
}

// custom types
export type Project = Omit<
  Schema.Work,
  | 'categories'
  | 'featuredImage'
  | 'hoverImages'
  | 'gallery'
  | 'monthYear'
  | 'workType'
> & {
  categories: Schema.WorkCategory[]
  featuredImage: ExtendedSanityImageObject
  hoverImages: ExtendedSanityImageObject[]
  gallery: GalleryItem[]
  monthYear: string
  workType: string
}
// custom types

// Fetch works/projects
export async function getAllProjects(
  sort: string = 'order(_createdAt desc)',
  last: number = 0,
  limit: number = 12
) {
  const rawData = await rawSanityClient.fetch<Project[]>(
    `*[_type == "work" && wasDeleted != true && isDraft != true] | ${sort} [${last}...${
      last + limit
    }] {
      ...,
      categories[]->{
        ...,
        name,
        value,
      }
    }`
  )

  if (rawData.length < 0) {
    return []
  }

  return rawData
}

export async function getProjectsNumber() {
  const rawData = await rawSanityClient.fetch<number>(
    `count(*[_type == "work" && wasDeleted != true && isDraft != true])`
  )

  return rawData
}

export async function getNextProject(
  sort: string = 'order(_createdAt desc)',
  last: number = 0,
  limit: number = 12
) {
  const rawData = await rawSanityClient.fetch<Project[]>(
    `*[_type == "work" && wasDeleted != true && isDraft != true] | ${sort} [${last}...${limit}] {
      ...,
      categories[]->{
        ...,
        name,
        value,
      }
    }`
  )

  if (rawData.length < 0) {
    return []
  }

  return rawData
}

export async function getProjectsById(ids: string[]) {
  const rawData = await rawSanityClient.fetch<Project[]>(
    `*[_id in [${ids.map((id) => `"${id}"`).join(',')}]] {
      _id,
      title,
      gallery[]{
        _type == 'photo' => {
          ${imageMeta}
        },
        _type == 'video' => {
          ...,
          asset{
            _ref,
            _type
          }
        },
        _type == 'vimeo' => {
          ...
        }
      }
    }`
  )

  return rawData
}

export async function getProject(slug: string) {
  const rawData = await rawSanityClient.fetch<{
    current: Project
    previous: { slug: string }
    next: { slug: string }
  }>(
    `*[_type == "work" && slug.current == "${slug}" && wasDeleted != true && isDraft != true] {
      "current": {
        ...,
        categories[]->{
          ...,
          name,
          value,
        },
        featuredImages{
          ${imageMeta}
        },
        hoverImages[]{
          ${imageMeta}
        },
        gallery[]{
          _type == 'photo' => {
            ${imageMeta}
          },
          _type == 'video' => {
            ...,
            asset{
              _ref,
              _type
            }
          },
          _type == 'vimeo' => {
            ...,
            thumbnail{
              ${imageMeta}
            }
          }
        },
        seo {
          metaTitle,
          metaDesc,
          shareTitle,
          shareDesc,
          shareGraphic
        }
      },
      "previous": *[_type == "work" && ^.monthYear > monthYear]|order(monthYear desc)[0]{
        "slug": slug.current
      },
      "next": *[_type == "work" && ^.monthYear < monthYear]|order(monthYear asc)[0]{
        "slug": slug.current
      },
    } | order(monthYear desc)[0]`
  )

  return rawData
}

// custom types
export type ExtendedHomeLayout = Omit<Schema.HomeLayout, 'gallery'> & {
  gallery: Project[]
}

export type PageResult = Omit<
  Schema.Page,
  'landingImage' | 'productionContents' | 'homeContents'
> & {
  landingImage: {
    _key: string
    _type: string
    title?: string
    image?: ExtendedSanityImageObject
  }
  landingWork: ExtendedHomeLayout
  homeContents: ExtendedHomeLayout[]
  productionContents: {
    _key: string
    _type: string
    title?: string
    content?: Schema.ComplexPortableText
    mediaType: 'image' | 'video'
    image?: ExtendedSanityImageObject
    video?: {
      asset: {
        _ref: string
        _type: string
      }
    }
  }[]
  filterCategory: WorkCategory[]
  seo: Schema.Seo
}
// custom types

// // Fetch a static page with our global data
export async function getStaticPage(filter: string) {
  const rawData = await rawSanityClient.fetch<PageResult>(
    `*[_type == "page" && ${filter} && wasDeleted != true && isDraft != true][0] {
      ...,
      landingWork{
        ...,
        gallery->{
          ...,
          featuredImages{
            ${imageMeta}
          },
          categories[]->{
            ...,
            name,
            value,
          },
        },
      },
      homeContents[]{
        ...,
        gallery[]->{
          ...,
          featuredImages{
            ${imageMeta}
          },
          categories[]->{
            ...,
            name,
            value,
          },
        },
      },
      productionContents[]{
        ...,
        mediaType,
        image{
          ${imageMeta}
        },
        video{
          asset{
            _ref,
            _type
          }
        },
      },
      filterCategory[] -> {
        ...,
        name,
        value,
      },
      seo {
        metaTitle,
        metaDesc,
        shareTitle,
        shareDesc,
        shareGraphic
      },
    }`
    // `*[_type == "page" && ${filter} && wasDeleted != true && isDraft != true][0] {
    //   ...,
    //   productionContents[]{
    //     ...,
    //     image{
    //       ${imageMeta}
    //     }
    //   }
    // }`
  )

  return rawData
}

// // Fetch all our page redirects
// export async function getAllRedirects() {
//   const data = await getSanityClient().fetch(
//     `*[_type == "redirect"]{ from, to }`
//   )
//   return data
// }

// // Fetch a specific dynamic page with our global data
// export async function getPage(slug, preview) {
//   const slugs = [`/${slug}`, slug, `/${slug}/`]

//   const query = `
//     {
//       "page": *[_type == "page" && slug.current in ${JSON.stringify(
//         slugs
//       )}] | order(_updatedAt desc)[0]{
//         title,
//         slug,
//         modules[]{
//           defined(_ref) => { ...@->content[0] {
//             ${queries.modules}
//           }},
//           !defined(_ref) => {
//             ${queries.modules},
//           }
//         },
//         seo
//       },
//       ${queries.site}
//     }
//     `

//   const data = await getSanityClient(preview).fetch(query)

//   return data
// }

// // Fetch a specific product with our global data
// export async function getProduct(slug, preview) {
//   const query = `
//     {
//       "page": *[_type == "product" && slug.current == "${slug}" && wasDeleted != true && isDraft != true] | order(_updatedAt desc)[0]{
//         hasTransparentHeader,
//         modules[]{
//           ${queries.modules}
//         },
//         "product": ${queries.product},
//         title,
//         seo
//       },
//       ${queries.site}
//     }
//   `

//   const data = await getSanityClient(preview).fetch(query)

//   return data
// }

// // Fetch a specific addon item
// export async function getAddon(id, preview) {
//   const query = `
//     {
//       "data": *[_id == ${JSON.stringify(id)}] {
//         "product": ${queries.product}
//       }
//     }
//   `

//   const data = await getSanityClient(preview).fetch(query)

//   return data?.data?.[0]?.product
// }

// // Fetch a specific collection with our global data
// export async function getCollection(slug, preview) {
//   const query = `
//     {
//       "page": *[_type == "collection" && slug.current == "${slug}"] | order(_updatedAt desc)[0]{
//         hasTransparentHeader,
//         modules[]{
//           ${queries.modules}
//         },
//         products[wasDeleted != true && isDraft != true${
//           preview?.active ? ' && _id in path("drafts.**")' : ''
//         }]->${queries.product},
//         title,
//         seo
//       },
//       ${queries.site}
//     }
//   `

//   const data = await getSanityClient(preview).fetch(query)

//   return data
// }

// export async function getAllHeaderProduct() {
//   const query = `
//     {
//       "products": *[_type == "product" && wasDeleted != true && isDraft != true] | order(_updatedAt desc)[]{
//         "product": {
//           "slug": slug.current,
//           "id": productID,
//           options,
//           title,
//           headerDescription,
//           galleryPhotos[] {
//             _key,
//             forVariant,
//             photo{
//               ${queries.imageMeta}
//            }
//           },
//           "variants": *[_type == "productVariant" && productID == ^.productID && wasDeleted != true && isDraft != true]{
//             "id": variantID,
//             title,
//             price,
//             comparePrice,
//             inStock,
//             options[]{
//               name,
//               position,
//               value
//             },
//           },
//         },
//         title
//       },
//     }
//   `

//   const data = await getSanityClient().fetch(query)

//   return data
// }

// export async function getAllProductsForOthers() {
//   const query = `
//     {
//       "products": *[_type == "product" && wasDeleted != true && isDraft != true] | order(_updatedAt desc)[]{
//         "product": {
//           "slug": slug.current,
//           "id": productID,
//           title,
//           galleryPhotos[] {
//             _key,
//             forVariant,
//             photo{
//               ${queries.imageMeta}
//            }
//           },
//         },
//         title
//       },
//     }
//   `

//   const data = await getSanityClient().fetch(query)

//   return data
// }

// export async function getAllSolidColors() {
//   const query = `
//     {
//       "colors": *[_type == "solidColor"] | order(_updatedAt desc)[]
//     }
//   `

//   const data = await getSanityClient().fetch(query)

//   return data
// }

// export async function getItemByQuery(query) {
//   const data = await getSanityClient().fetch(query)

//   return data
// }

// imageMeta
const imageMeta = `
  ...,
  "id": asset->assetId,
  "type": asset->mimeType,
  "aspectRatio": asset->metadata.dimensions.aspectRatio,
  "lqip": asset->metadata.lqip
`
