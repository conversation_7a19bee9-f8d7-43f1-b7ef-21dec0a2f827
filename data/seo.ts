import { rawSanityClient } from '@lib/sanity'

export interface SeoSettings {
  metaTitle?: string
  metaDesc?: string
  shareTitle?: string
  shareDesc?: string
  shareGraphic?: {
    asset?: {
      _ref: string
    }
  }
}

export interface GeneralSiteSettings {
  siteTitle?: string
  siteURL?: string
  lockdown?: boolean
}

export async function getSeoSettings(): Promise<SeoSettings> {
  const data = await rawSanityClient.fetch<SeoSettings>(
    `*[_type == "seoSettings"][0]{
      metaTitle,
      metaDesc,
      shareTitle,
      shareDesc,
      shareGraphic
    }`
  )

  return data || {}
}

export async function getGeneralSettings(): Promise<GeneralSiteSettings> {
  const data = await rawSanityClient.fetch<GeneralSiteSettings>(
    `*[_type == "generalSettings"][0]{
      siteTitle,
      siteURL,
      lockdown
    }`
  )

  return data || {}
}
