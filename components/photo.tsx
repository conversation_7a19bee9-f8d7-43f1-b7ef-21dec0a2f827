import { SanityImageObject } from '@sanity/image-url/lib/types/types'
import React, { FC } from 'react'
import NextImage from 'next/image'
import { useNextSanityImage } from 'next-sanity-image'

import { rawSanityClient } from '@lib/sanity'
import MotionBox from '@components/motion-box'
import { fadeAnim } from 'data/animation'
import { Box, Center, CloseButton, Image, Text, VStack } from '@chakra-ui/react'
import { useSnapshot } from 'valtio'
import lightboxStore from 'data/store'
import { AnimatePresence, motion } from 'framer-motion'
import { buildSrcSet } from '@lib/helpers'

export interface ExtendedSanityImageObject extends SanityImageObject {
  _key: string
  _type: 'photo'
  aspectRatio?: number
  customRatio?: number
  alt?: string
  id: string
  lqip: string
  type: string
}

export interface ExtendedSanityImageObjectOrVimeo {
  _key: string
  _type: 'vimeo'
  vimeo_id?: string
  thumbnail?: ExtendedSanityImageObject
  customRatio?: number
  aspectRatio?: number
}

export interface ExtendedSanityVideoObject {
  _key: string
  _type: 'video'
  asset: {
    _ref: string
    _type: string
  }
}

export type GalleryItem = ExtendedSanityImageObject | ExtendedSanityImageObjectOrVimeo | ExtendedSanityVideoObject

export type ImgElementStyle = NonNullable<JSX.IntrinsicElements['img']['style']>

const Photo: FC<{
  edit?: boolean
  photo: ExtendedSanityImageObject
  layout?: 'fill' | 'fixed' | 'intrinsic' | 'responsive' | 'raw' | undefined
  objectFit?: ImgElementStyle['objectFit']
  objectPosition?: ImgElementStyle['objectPosition']
  isThumbnail?: boolean
  useChakra?: boolean
  galleryMode?: boolean
  withPlaceholder?: boolean
}> = ({
  edit,
  photo,
  layout = 'responsive',
  objectFit = 'contain',
  objectPosition = 'center',
  isThumbnail = false,
  useChakra = false,
  galleryMode = false,
  withPlaceholder = false,
}) => {
  const srcSizes = [400, 600, 800, 1000, 1200]
  const quality = isThumbnail ? 100 : 100
  const aspect = photo?.aspectRatio
    ? photo.aspectRatio >= 1
      ? 4 / 3
      : 3 / 2
    : 3 / 2

  const myCustomImageBuilder = (imageUrlBuilder: any, options: any) => {
    if (useChakra) {
      return imageUrlBuilder.width(
        Math.min(options.originalImageDimensions.width, 600)
      )
    }

    if (isThumbnail) {
      return imageUrlBuilder.width(
        Math.min(options.originalImageDimensions.width, 1200)
      )
    }

    return imageUrlBuilder.width(options.originalImageDimensions.width)
  }

  const imageProps = useNextSanityImage(rawSanityClient, photo, {
    blurUpImageWidth: 124,
    blurUpImageQuality: 40,
    blurUpAmount: 24,
    imageBuilder: myCustomImageBuilder,
  })
  const chakraImageProps = useNextSanityImage(rawSanityClient, photo, {
    imageBuilder: (imageUrlBuilder) => {
      return imageUrlBuilder.width(200).quality(0)
    },
  })
  const { mediaSelected } = useSnapshot(lightboxStore)
  if (!photo?.asset) return null

  const removeImage = () => {
    const index = mediaSelected.findIndex((image) => image.id == photo.id)
    lightboxStore.mediaSelected.splice(index, 1)
  }

  const srcset = buildSrcSet(photo, {
    ...{ srcSizes },
    ...{ aspect },
    ...{ quality },
  })

  if (galleryMode) {
    return (
      <Image
        position="relative"
        src={imageProps.src}
        alt={photo.alt}
        maxH="80%"
      />
    )
  }

  if (useChakra) {
    return (
      <Box h="full">
        <MotionBox
          initial="hide"
          animate="show"
          exit="hide"
          variants={fadeAnim}
          position="absolute"
          top="50%"
          transform="translateY(-50%)"
        >
          <Image
            position="relative"
            src={imageProps.src}
            alt={photo.alt}
            objectFit="cover"
          />

          {edit && (
            <CloseButton
              position="absolute"
              top="-16px"
              right="-16px"
              color="white"
              onClick={removeImage}
              _hover={{}}
            />
          )}
        </MotionBox>

        {/* used as image size */}
        {withPlaceholder && (
          <Image opacity={0} src={chakraImageProps.src} alt={photo.alt} />
        )}
      </Box>
    )
  }

  if (layout == 'fill') {
    return (
      <MotionBox
        justifyContent={'flex-end !important'}
        alignItems={'flex-start !important'}
        initial="hide"
        animate="show"
        exit="hide"
        variants={fadeAnim}
      >
        <NextImage
          layout="fill"
          src={imageProps.src}
          sizes={srcset}
          alt={photo.alt}
          objectFit={objectFit}
          objectPosition={objectPosition}
          decoding="async"
          draggable={false}
          priority
          // placeholder={imageProps.placeholder}
          // blurDataURL={imageProps.blurDataURL}
          // blurDataURL={photo.lqip}
        />
      </MotionBox>
    )
  }

  return (
    <MotionBox initial="hide" animate="show" exit="hide" variants={fadeAnim}>
      <NextImage
        {...imageProps}
        layout={layout}
        alt={photo.alt}
        objectFit={objectFit}
        objectPosition={objectPosition}
        placeholder="empty"
        decoding="async"
        draggable={false}
        priority
        // blurDataURL={photo.lqip}
      />
    </MotionBox>
  )

  // return (
  //   <NextImage
  //     width={width}
  //     height={height}
  //     layout={layout}
  //     src={src}
  //     alt={photo.alt}
  //     draggable={false}
  //     objectFit={objectFit}
  //     objectPosition={objectPosition}
  //     placeholder={photo.lqip != null ? 'blur' : 'empty'}
  //     blurDataURL={photo.lqip}
  //   />
  // )
}

export default Photo
