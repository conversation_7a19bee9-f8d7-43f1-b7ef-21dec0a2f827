import React, { FC, ReactNode, useEffect, useState } from 'react'
import { Keyframes, Scroll, useScrollState } from 'scrollex'
import {
  Box,
  chakra,
  LinkBox,
  LinkOverlay,
  Text,
  useBreakpointValue,
} from '@chakra-ui/react'
import _ from 'lodash'

import LayoutA from 'layouts/layout_a'
import LayoutB from 'layouts/layout_b'
import LayoutC from 'layouts/layout_c'
import LayoutD from 'layouts/layout_d'
import { ExtendedHomeLayout } from '@data'
import { headerColor } from '@components/header'
import MotionBox from '@components/motion-box'
import Photo, { ExtendedSanityImageObject } from '@components/photo'
import { WorkCategory } from 'studio/schema'
import Link from 'next/link'

const ScrollItem = chakra(Scroll.Item)
const ScrollSection = chakra(Scroll.Section)
const ScrollContainer = chakra(Scroll.Container)

const keyframes: Record<string, Keyframes> = {
  imageContainer: ({ section }) => {
    return {
      [section.topAt('container-bottom')]: {
        opacity: 0,
      },
      [section.topAt('container-center')]: {
        opacity: 1,
      },
      [section.topAt('container-top')]: {
        opacity: 1,
      },
      [section.bottomAt('container-top')]: {
        opacity: 0,
      },
    }
  },
}

const Slider: FC<{ children?: ReactNode; slides: ExtendedHomeLayout[] }> = ({
  children,
  slides,
}) => {
  const responsiveHeight = useBreakpointValue({
    base: 'auto',
    sm: 'var(--chakra-vh)',
  })
  const responsiveSnap = useBreakpointValue({
    base: 'none',
    sm: 'y mandatory',
  })

  return (
    <ScrollContainer
      throttleAmount={0}
      scrollAxis="y"
      height="var(--chakra-vh)"
      width="100vw"
      scrollSnapType={responsiveSnap}
    >
      {children && (
        <>
          <ScrollSection height={responsiveHeight} scrollSnapAlign="start">
            <LandingSlide>{children}</LandingSlide>
          </ScrollSection>
          {slides.map((slide, index) => {
            return (
              <ScrollSection
                key={index}
                height={responsiveHeight}
                scrollSnapAlign="start"
              >
                <ScrollItem keyframes={keyframes.imageContainer}>
                  <ScrollItem>
                    <>
                      {slide.type == 'a' && <LayoutA slide={slide} />}
                      {slide.type == 'b' && <LayoutB slide={slide} />}
                      {slide.type == 'c' && <LayoutC slide={slide} />}
                      {slide.type == 'd' && <LayoutD slide={slide} />}
                    </>
                  </ScrollItem>
                </ScrollItem>
              </ScrollSection>
            )
          })}
        </>
      )}
    </ScrollContainer>
  )
}

export const SliderCard: FC<{
  image: ExtendedSanityImageObject
  categories: WorkCategory[]
  title: string
  slug: string
  hoverColor?: string
}> = ({ image, categories, title, slug, hoverColor = '#E6B976' }) => {
  const whileHover = useBreakpointValue({ base: undefined, sm: 'hover' })

  return (
    <LinkBox pos="relative" w="100%" h="100%">
      <MotionBox
        variants={{
          initial: { opacity: 1 },
          hover: { opacity: 1 },
        }}
        pos="relative"
        w="100%"
        h="100%"
        initial="initial"
        whileHover={whileHover}
        cursor="pointer"
      >
        <Link href={`/works/${slug}`} passHref>
          <LinkOverlay>
            <Photo photo={image} layout="fill" objectFit="cover" />
            <MotionBox
              sx={{
                position: 'relative',
                bg: hoverColor,
                w: '100%',
                h: '100%',
              }}
              variants={{
                initial: { opacity: 0 },
                hover: { opacity: 1 },
              }}
              p={7}
            >
              <Text textStyle="p">{title}</Text>
              <Text textStyle="p" opacity="0.5">
                {categories?.flatMap(
                  (category, index) =>
                    `${index > 0 ? ',' : ''} ${category.name}`
                )}
              </Text>
            </MotionBox>
          </LinkOverlay>
        </Link>
      </MotionBox>
    </LinkBox>
  )
}

const LandingSlide: FC<{ children?: ReactNode }> = ({ children }) => {
  // This component rerenders whenever selectedIndex changes
  const hasScrolledOver = useScrollState(({ section, position }) => {
    if (position > section.bottomAt('container-top') / 3) {
      return true
    }
    return false
  })

  useEffect(() => {
    if (hasScrolledOver) {
      headerColor.color = '#000'
    } else {
      headerColor.color = '#fff'
    }
  }, [hasScrolledOver])

  return (
    <ScrollItem keyframes={keyframes.imageContainer}>
      <ScrollItem keyframes={keyframes.image}>{children}</ScrollItem>
    </ScrollItem>
  )
}

export default Slider
