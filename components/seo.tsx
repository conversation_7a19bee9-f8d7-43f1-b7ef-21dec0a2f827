import Head from 'next/head'
import { useRouter } from 'next/router'

interface SEOProps {
  title?: string
  description?: string
  image?: string
  defaultSEO?: {
    metaTitle?: string
    metaDesc?: string
    shareGraphic?: {
      asset?: {
        _ref: string
      }
    }
  }
  siteTitle?: string
}

const SEO = ({
  title,
  description,
  image,
  defaultSEO,
  siteTitle = 'Ooze',
}: SEOProps) => {
  const router = useRouter()

  // Use provided values or fall back to defaults
  const metaTitle = title || defaultSEO?.metaTitle || siteTitle
  const metaDescription = description || defaultSEO?.metaDesc || ''

  // Construct the image URL from Sanity if available
  let metaImage = image
  if (!metaImage && defaultSEO?.shareGraphic?.asset?._ref) {
    // Parse the Sanity image reference
    const ref = defaultSEO.shareGraphic.asset._ref
    const [_file, id, extension] = ref.split('-')

    // Convert extension if needed (e.g., 'jpg' from 'jpg')
    const ext = extension === 'jpeg' ? 'jpg' : extension

    // Construct the Sanity CDN URL
    metaImage = `https://cdn.sanity.io/images/${
      process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'undefined'
    }/${
      process.env.NEXT_PUBLIC_SANITY_PROJECT_DATASET || 'production'
    }/${id}.${ext}`
  }

  // Construct the canonical URL
  const canonicalUrl = `${
    process.env.NEXT_PUBLIC_SITE_URL || 'https://ooze.sg'
  }${router.asPath === '/' ? '' : router.asPath}`.split('?')[0]

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{metaTitle}</title>
      <meta name="description" content={metaDescription} />
      <link rel="canonical" href={canonicalUrl} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content="website" />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:title" content={metaTitle} />
      <meta property="og:description" content={metaDescription} />
      {metaImage && <meta property="og:image" content={metaImage} />}

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={canonicalUrl} />
      <meta property="twitter:title" content={metaTitle} />
      <meta property="twitter:description" content={metaDescription} />
      {metaImage && <meta property="twitter:image" content={metaImage} />}
    </Head>
  )
}

export default SEO
