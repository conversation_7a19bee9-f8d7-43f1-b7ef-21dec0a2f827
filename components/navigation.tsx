import {
  Box,
  Flex,
  HStack,
  IconButton,
  Text,
  useBreakpointValue,
  useColorModeValue,
} from '@chakra-ui/react'
import { HamburgerIcon } from '@chakra-ui/icons'
import React, { FC, useMemo } from 'react'
import { useSnapshot } from 'valtio'

import lightboxStore from 'data/store'
import NavLink from '@components/nav-link'
import { headerColor } from './header'
import { useRouter } from 'next/router'

interface NavigationProps {
  theme?: string
  onOpen?: () => void
}

const Navigation: FC<NavigationProps> = ({ theme, onOpen }) => {
  const router = useRouter()
  const { mediaSelected } = useSnapshot(lightboxStore)
  const { color } = useSnapshot(headerColor)
  const themeColor = useMemo(() => {
    if (router?.pathname == '/') {
      return color
    }
    return theme == 'dark' ? '#fff' : '#000'
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [theme, color])

  const responsiveColor = useBreakpointValue({ base: '#000', sm: themeColor })

  return (
    <Box>
      <HStack
        justifyContent="flex-end"
        alignItems="center"
        w="100%"
        h="100%"
        display={['none', null, null, 'flex']}
        spacing={30}
      >
        <NavLink to="/works" color={responsiveColor}>
          Work
        </NavLink>
        <NavLink to="/production" color={responsiveColor}>
          Production
        </NavLink>
        <NavLink to="/about" color={responsiveColor}>
          About
        </NavLink>
        <Text textStyle="body" color={responsiveColor}>
          |
        </Text>
        <NavLink to="/lightbox" color={responsiveColor}>
          LIGHTBOX ({mediaSelected.length})
        </NavLink>
      </HStack>

      <Flex
        justifyContent="flex-end"
        w="100%"
        display={['flex', null, null, 'none']}
      >
        <IconButton
          bg="transparent"
          color={responsiveColor}
          aria-label="Menu"
          size="lg"
          icon={<HamburgerIcon />}
          onClick={onOpen}
        />
      </Flex>
    </Box>
  )
}

export default Navigation
