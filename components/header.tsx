import {
  <PERSON><PERSON>,
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
  useDisclosure,
  DrawerHeader,
  IconButton,
  Text,
  VStack,
  <PERSON>er<PERSON><PERSON>er,
  <PERSON>,
  HStack,
} from '@chakra-ui/react'
import React, { FC } from 'react'
import { proxy } from 'valtio'

import Navigation from '@components/navigation'
import Logo from '@components/logo'
import NavLink from '@components/nav-link'
import { CloseIcon } from '@chakra-ui/icons'
import { useRouter } from 'next/router'

type HeaderProps = {
  theme?: string
}

export const headerColor = proxy({ color: '#fff' })
const Header: FC<HeaderProps> = ({ theme }) => {
  const { isOpen, onOpen, onClose } = useDisclosure()
  const router = useRouter()
  // const btnRef = React.useRef(null)

  return (
    <>
      <Grid
        position="fixed"
        top={0}
        left={0}
        right={0}
        templateColumns={['1fr 1fr', null, null, '1fr 11fr']}
        gap={4}
        px={[4, null, null, 8]}
        py={[4, null, null, 7]}
        zIndex={90}
        bg={
          router.asPath == '/works'
            ? 'white'
            : router.asPath == '/lightbox'
            ? 'brand.gray.500'
            : 'transparent'
        }
        // mixBlendMode="difference"
      >
        <Logo theme={theme} />
        <Navigation theme={theme} onOpen={onOpen} />
      </Grid>

      <Drawer
        size="full"
        isOpen={isOpen}
        placement="right"
        onClose={onClose}
        // finalFocusRef={btnRef}
      >
        <DrawerOverlay />
        <DrawerContent bg="brand.gray.200" height="var(--chakra-vh) !important">
          <DrawerHeader
            px={5}
            py={4}
            display="flex"
            justifyContent="space-between"
          >
            <Logo theme={theme == 'dark' ? 'light' : 'dark'} />
            <IconButton
              bg="transparent"
              aria-label="Close"
              size="lg"
              icon={<CloseIcon />}
              onClick={onClose}
            />
          </DrawerHeader>

          <DrawerBody>
            <VStack pt={14}>
              <NavLink to="/works" color="brand.gray.400">
                <Text color="brand.gray.400" textStyle="menu">
                  Work
                </Text>
              </NavLink>
              <NavLink to="/production" color="brand.gray.400">
                <Text color="brand.gray.400" textStyle="menu">
                  Production
                </Text>
              </NavLink>
              <NavLink to="/about" color="brand.gray.400">
                <Text color="brand.gray.400" textStyle="menu">
                  About
                </Text>
              </NavLink>
            </VStack>
          </DrawerBody>

          <DrawerFooter h="30vh" justifyContent="center">
            <VStack spacing={5}>
              <Text textStyle="h3">Contact Us</Text>
              <HStack spacing={6}>
                <Link
                  href="https://www.instagram.com/studio.oooze/"
                  target="_blank"
                >
                  Instagram
                  {/* <IconButton aria-label="instagram" bg="transparent">
                    <svg
                      width="28"
                      height="27"
                      viewBox="0 0 28 27"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clipPath="url(#clip0_313_827)">
                        <path
                          d="M24.3459 4.58932C24.3459 4.07829 24.143 3.58837 23.7816 3.22702C23.4203 2.86566 22.9301 2.66251 22.4191 2.66251C21.9081 2.66251 21.4179 2.86566 21.0566 3.22702C20.6952 3.58837 20.4923 4.07829 20.4923 4.58932C20.4923 5.10035 20.6952 5.59048 21.0566 5.95184C21.4179 6.31319 21.9081 6.51635 22.4191 6.51635C22.9273 6.50735 23.4121 6.30143 23.7716 5.942C24.131 5.58258 24.3369 5.09754 24.3459 4.58932ZM22.1037 13.1027C22.1037 11.4813 21.6229 9.89629 20.7221 8.54815C19.8213 7.20001 18.541 6.14915 17.043 5.52867C15.545 4.90819 13.8967 4.74599 12.3065 5.06231C10.7162 5.37863 9.25551 6.15931 8.10901 7.3058C6.96252 8.4523 6.18184 9.91303 5.86552 11.5033C5.5492 13.0935 5.7114 14.742 6.33188 16.24C6.95236 17.738 8.00322 19.0179 9.35136 19.9187C10.6995 20.8195 12.2845 21.3005 13.9059 21.3005C16.0801 21.3005 18.1652 20.437 19.7026 18.8996C21.24 17.3622 22.1037 15.2769 22.1037 13.1027ZM27.0436 13.1027C27.0436 14.9245 27.0436 16.6059 27.0436 18.5678C27.0428 19.6266 26.9371 20.6828 26.7282 21.7209C26.4959 22.8121 25.9288 23.8035 25.1061 24.5571C24.2834 25.3107 23.2461 25.7892 22.1388 25.9252C21.2805 26.0604 20.4147 26.1421 19.5463 26.1702H12.1542H7.56482C6.53586 26.1569 5.51664 25.9672 4.55175 25.6096C3.73588 25.2953 3.00928 24.7864 2.43527 24.1269C1.86127 23.4674 1.45729 22.6775 1.25857 21.8261C1.01329 20.8057 0.90735 19.7568 0.94339 18.708C0.94339 15.2046 0.94339 12.0866 0.94339 8.75834C0.94339 7.6723 0.943347 6.55139 1.11852 5.43031C1.17627 4.6387 1.39933 3.868 1.77347 3.168C2.14761 2.46799 2.6645 1.85412 3.29059 1.36628C4.10723 0.77745 5.05902 0.403941 6.05818 0.280238C7.50842 0.0754359 8.97336 -0.0066263 10.4374 0.0349763H18.1449C19.336 0.0349763 20.5272 0.0349677 21.6482 0.24517C22.4389 0.307154 23.2081 0.532294 23.9076 0.906116C24.607 1.27994 25.2215 1.79433 25.7123 2.41725C26.3011 3.23389 26.6746 4.18589 26.7983 5.18505C27.0031 6.6588 27.0851 8.14686 27.0436 9.63419V13.1378"
                          fill="#A5AAAE"
                        />
                        <path
                          d="M8.58044 13.1023C8.5735 12.0407 8.88201 11.0008 9.46676 10.1147C10.0515 9.22863 10.886 8.53631 11.8649 8.12523C12.8437 7.71416 13.9227 7.60292 14.9647 7.80577C16.0068 8.00862 16.9651 8.5163 17.7182 9.26451C18.4713 10.0127 18.9854 10.9677 19.1951 12.0084C19.4048 13.0491 19.3006 14.1287 18.896 15.1102C18.4913 16.0917 17.8043 16.9308 16.9221 17.5213C16.0399 18.1119 15.0021 18.4275 13.9405 18.4275C13.2382 18.4321 12.5421 18.2975 11.892 18.0319C11.2419 17.7664 10.6507 17.3752 10.1525 16.8802C9.65431 16.3853 9.25886 15.7966 8.98906 15.1482C8.71926 14.4999 8.58042 13.8046 8.58044 13.1023Z"
                          fill="#A5AAAE"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_313_827">
                          <rect
                            width="26.2052"
                            height="26.2052"
                            fill="white"
                            transform="translate(0.803223)"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </IconButton> */}
                </Link>
                <Link href="mailto:<EMAIL>">
                  Email
                  {/* <IconButton aria-label="email" bg="transparent">
                    <svg
                      width="31"
                      height="22"
                      viewBox="0 0 31 22"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0.722233 0.865051C0.334808 0.901614 0.00644564 1.26373 0.00855503 1.65256V20.5527C0.00855503 20.9648 0.384027 21.3402 0.796062 21.3402H29.9338C30.3458 21.3402 30.7213 20.9648 30.7213 20.5527V1.65256C30.7213 1.24052 30.3458 0.865051 29.9338 0.865051H0.722233ZM2.9617 2.44006H27.7682L15.3649 12.4069L2.9617 2.44006ZM1.58357 3.39984L11.1447 11.0288L1.58357 18.7443V3.39984ZM29.1463 3.39984V18.7443L19.5851 11.0288L29.1463 3.39984ZM12.4244 12.0751L14.8488 14.0684C15.1357 14.3061 15.5956 14.3061 15.8824 14.0684L18.3068 12.0751L27.8181 19.7659H2.91249L12.4244 12.0751Z"
                        fill="#A5AAAE"
                      />
                    </svg>
                  </IconButton> */}
                </Link>
                <Link href="https://vimeo.com/oooze" isExternal>
                  Vimeo
                </Link>
              </HStack>
            </VStack>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  )
}

export default Header
