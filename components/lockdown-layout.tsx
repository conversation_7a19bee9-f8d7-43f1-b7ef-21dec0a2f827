import React from 'react'
import { Box, VStack, Text, Center, useColorModeValue } from '@chakra-ui/react'
import { Chakra } from '@lib/chakra'

type Props = {
  cookies?: string
  children?: React.ReactNode
  siteTitle?: string
}

const LockdownLayout = ({ cookies, children, siteTitle }: Props) => {
  const bg = useColorModeValue('gray.50', 'gray.900')
  const textColor = useColorModeValue('gray.800', 'gray.100')
  const accentColor = useColorModeValue('gray.600', 'gray.400')

  return (
    <Chakra cookies={cookies}>
      <Box bg={bg} minH="100vh">
        <Center minH="100vh">
          <VStack spacing={8} textAlign="center" px={8} maxW="xl">
            <VStack spacing={4}>
              <Text
                fontSize="6xl"
                fontWeight="bold"
                color={textColor}
                letterSpacing="tight"
              >
                🔒
              </Text>
              <Text
                fontSize="2xl"
                fontWeight="bold"
                color={textColor}
                letterSpacing="tight"
              >
                Site Under Maintenance
              </Text>
            </VStack>

            <VStack spacing={3}>
              <Text fontSize="lg" color={accentColor} lineHeight="tall">
                {siteTitle || 'This site'} is currently in maintenance mode.
              </Text>
              <Text fontSize="md" color={accentColor} lineHeight="tall">
                We're working on some exciting updates and will be back soon!
              </Text>
            </VStack>

            {children && <Box mt={8}>{children}</Box>}
          </VStack>
        </Center>
      </Box>
    </Chakra>
  )
}

export default LockdownLayout
