import React, { <PERSON> } from 'react'
import { PortableText } from '@portabletext/react'
import { OrderedList, Text, UnorderedList } from '@chakra-ui/react'
import _ from 'lodash'

import Photo from '@components/photo'

const Content: FC<{ blocks: any }> = ({ blocks }) => {
  if (!blocks) return null

  return (
    <PortableText
      // renderContainerOnSingleChild
      value={blocks}
      components={{
        block: {
          h1: ({ children, value: { markDefs } }) => (
            <Text as="h1" textStyle="h1" textAlign={setTextAlign(markDefs)}>
              {children}
            </Text>
          ),
          h2: ({ children, value: { markDefs } }) => (
            <Text as="h2" textStyle="h2" textAlign={setTextAlign(markDefs)}>
              {children}
            </Text>
          ),
          h3: ({ children, value: { markDefs } }) => (
            <Text as="h3" textStyle="h3" textAlign={setTextAlign(markDefs)}>
              {children}
            </Text>
          ),
          body: ({ children, value: { markDefs } }) => (
            <Text textStyle="body" textAlign={setTextAlign(markDefs)}>
              {children}
            </Text>
          ),
          subtitle: ({ children, value: { markDefs } }) => (
            <Text
              opacity={0.5}
              textStyle="body"
              textAlign={setTextAlign(markDefs)}
            >
              {children}
            </Text>
          ),
          em: ({ children, value: { markDefs } }) => (
            <Text textStyle="italic" textAlign={setTextAlign(markDefs)}>
              {children}
            </Text>
          ),
        },
        list: {
          bullet: ({ children }) => (
            <UnorderedList marginInlineStart="1.3em">{children}</UnorderedList>
          ),
          number: ({ children }) => (
            <OrderedList marginInlineStart="1.1em">{children}</OrderedList>
          ),
        },
        types: {
          break: () => <br />,
          photo: ({ value }) => {
            return <Photo photo={value} />
          },
        },
      }}
    />
  )
}

const setTextAlign = (marks: any) => {
  let textAlign = null

  if (!_.isEmpty(marks)) {
    textAlign = _.find(marks, (markDef: any) => {
      return markDef._type == 'alignment'
    })?.styles?.textAlign
  }

  return textAlign
}

export default Content
