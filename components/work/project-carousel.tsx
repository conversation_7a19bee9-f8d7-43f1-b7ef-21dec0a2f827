import React, { FC, useEffect, useRef, useState } from 'react'
import { <PERSON>wiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Swiper as SwiperType } from 'swiper'
import {
  Grid,
  GridItem,
  AspectRatio,
  Box,
  Button,
  Flex,
  IconButton,
  Spacer,
  Text,
  useInterval,
  HStack,
} from '@chakra-ui/react'
import { useSnapshot } from 'valtio'
import { AnimatePresence, motion } from 'framer-motion'
import _ from 'lodash'

import lightboxStore from 'data/store'
import { Project } from '@data'
import Photo, { ExtendedSanityImageObject } from '@components/photo'
import MotionBox from '@components/motion-box'
import Link from 'next/link'
import { DateTime } from 'luxon'
import ReactPlayer from 'react-player'
import { useIsClient } from 'usehooks-ts'
import { fadeAnim } from 'data/animation'
import { useRouter } from 'next/router'

const ProjectCarousel: FC<{
  project: Project
  next?: string
  previous?: string
}> = ({ project, next, previous }) => {
  const router = useRouter()
  const [activeIndex, setActiveIndex] = useState(0)
  const [showMasonry, setShowMasonry] = useState(false)
  const [swiper, setSwiper] = useState<SwiperType | null>(null)

  useEffect(() => {
    swiper?.slideTo(0, 0)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [project])

  useEffect(() => {
    // on page change
    setActiveIndex(0)
  }, [router.asPath])

  const isClient = useIsClient()
  if (!isClient) {
    return <></>
  }

  return (
    <AnimatePresence>
      <Swiper
        spaceBetween={0}
        slidesPerView={1}
        onInit={(swiper) => setSwiper(swiper)}
        onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
        style={{
          height: '100%',
        }}
        modules={[Navigation, Pagination]}
        navigation={{
          nextEl: '.custom-button-next',
          prevEl: '.custom-button-prev',
        }}
        pagination={{
          type: 'custom',
          el: '.custom-pagination',
          renderCustom: function (swiper, current, total) {
            return `View All — ${current + '-' + total}`
          },
        }}
      >
        {project.gallery?.map((image, index) => {
          return (
            <SwiperSlide key={image._key + index}>
              <Box
                w="100%"
                // w={['100%', null, null, 'calc(100vw / 12 * 6)']}
                pb={[0, 0, 0, 'calc(100vh / 12 + 16px)']}
                pt={[8, 8, 8, 'calc(100vh / 12 + 16px)']}
                h="100%"
                mx="auto"
              >
                <AspectRatio
                  ratio={
                    image._type === 'video'
                      ? 16 / 9 // Default aspect ratio for videos
                      : image._type === 'vimeo' || image._type === 'photo'
                      ? (image.customRatio ?? 0) > 0
                        ? image.customRatio
                        : image.aspectRatio
                      : 16 / 9
                  }
                  h="100%"
                >
                  {image._type === 'vimeo' && image.vimeo_id ? (
                    <SwiperVideo url={`https://vimeo.com/${image.vimeo_id}`} />
                  ) : image._type === 'video' && image.asset ? (
                    <SwiperVideo
                      videoAsset={
                        image.asset as { _ref: string; _type: string }
                      }
                    />
                  ) : image._type === 'photo' ? (
                    <Photo photo={image} layout="fill" />
                  ) : null}
                </AspectRatio>
              </Box>
            </SwiperSlide>
          )
        })}
        <SwiperSlide>
          <Box
            display="flex"
            w={['100%', null, null, 'calc(100vw / 12 * 6)']}
            my={[
              'calc(100vh / 19 + 16px)',
              null,
              null,
              'calc(100vh / 12 + 16px)',
            ]}
            h="80%"
            mx="auto"
            bg={project.themeColor?.hex ?? '#FAC86D'}
          >
            <Flex flex={1} direction="column" textAlign="center">
              {next && (
                <Box pt={6}>
                  <Link href={`/works/${next}`} passHref>
                    <a>
                      <Text as="h3" textStyle="h3">
                        Next Project
                      </Text>
                    </a>
                  </Link>
                </Box>
              )}
              <Spacer />
              <Box>
                <Text as="h1" textStyle="h1">
                  {project.title}
                  {/* {project.workType && (
                    <>
                      <br />
                      {project.workType}
                    </>
                  )}
                  {project.monthYear && (
                    <>
                      <br />
                      {DateTime.fromFormat(
                        project.monthYear,
                        'yyyy-MM-dd'
                      ).toLocaleString(DateTime.DATE_FULL)}
                    </>
                  )} */}
                </Text>
              </Box>
              <Spacer />
              {previous && (
                <Box pb={6}>
                  <Link href={`/works/${previous}`} passHref>
                    <a>
                      <Text as="h3" textStyle="h3">
                        Previous Project
                      </Text>
                    </a>
                  </Link>
                </Box>
              )}
            </Flex>
          </Box>
        </SwiperSlide>

        <SwiperActions {...{ project, setShowMasonry, activeIndex }} />
        <SwiperNavigation
          activeIndex={activeIndex}
          totalSlide={project?.gallery?.length}
        />
      </Swiper>
      <MobileNav />
      {showMasonry && (
        <Box pos="absolute" bg="white" inset={0} zIndex={2} overflowX="hidden">
          <Flex
            display={['none', 'none', 'none', 'flex']}
            px={24}
            pt="calc(100vh / 12 + 48px)"
            pb={24}
            alignContent="center"
            justifyContent="center"
            flexWrap="wrap"
          >
            {project.gallery.map((image, index) => {
              return (
                <motion.div
                  key={image._key}
                  initial="hide"
                  animate="show"
                  exit="hide"
                  variants={fadeAnim}
                  onClick={() => {
                    swiper?.slideTo(index, 0)
                    setShowMasonry(false)
                  }}
                  style={{
                    cursor: 'pointer',
                    position: 'relative',
                  }}
                >
                  <Box
                    sx={{
                      img: {
                        height: '200px',
                      },
                    }}
                    p={'11px'}
                  >
                    {image._type === 'vimeo' && image.thumbnail ? (
                      <Photo
                        photo={image.thumbnail}
                        isThumbnail
                        useChakra
                        withPlaceholder
                      />
                    ) : image._type === 'photo' ? (
                      <Photo
                        photo={image}
                        isThumbnail
                        useChakra
                        withPlaceholder
                      />
                    ) : image._type === 'video' ? (
                      <Box
                        bg="gray.200"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        h="200px"
                        color="gray.600"
                      >
                        Video
                      </Box>
                    ) : null}
                  </Box>
                </motion.div>
              )
            })}
          </Flex>
          <Grid
            display={['grid', 'grid', 'grid', 'none']}
            templateColumns="repeat(2, 1fr)"
            gap={2}
            px={4}
            py={28}
          >
            <GridItem>
              {project.gallery.map((image, index) => {
                // some how need this hack to enable masonry
                return (
                  <>
                    {index % 2 == 0 && (
                      <motion.div
                        key={image._key}
                        initial="hide"
                        animate="show"
                        exit="hide"
                        variants={fadeAnim}
                        onClick={() => {
                          swiper?.slideTo(index, 0)
                          setShowMasonry(false)
                        }}
                        style={{
                          width: '100%',
                          marginBottom: 8,
                          display: 'inline-block',
                        }}
                      >
                        {image._type === 'vimeo' && image.thumbnail ? (
                          <Photo photo={image.thumbnail} isThumbnail />
                        ) : image._type === 'photo' ? (
                          <Photo photo={image} isThumbnail />
                        ) : image._type === 'video' ? (
                          <Box
                            bg="gray.200"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                            h="200px"
                            color="gray.600"
                          >
                            Video
                          </Box>
                        ) : null}
                      </motion.div>
                    )}
                  </>
                )
              })}
            </GridItem>

            <GridItem>
              {project.gallery.map((image, index) => {
                return (
                  <>
                    {index % 2 == 1 && (
                      <motion.div
                        key={image._key}
                        initial="hide"
                        animate="show"
                        exit="hide"
                        variants={fadeAnim}
                        onClick={() => {
                          swiper?.slideTo(index, 0)
                          setShowMasonry(false)
                        }}
                        style={{
                          width: '100%',
                          marginBottom: 8,
                          display: 'inline-block',
                        }}
                      >
                        {image._type === 'vimeo' && image.thumbnail ? (
                          <Photo photo={image.thumbnail} isThumbnail />
                        ) : image._type === 'photo' ? (
                          <Photo photo={image} isThumbnail />
                        ) : image._type === 'video' ? (
                          <Box
                            bg="gray.200"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                            h="200px"
                            color="gray.600"
                          >
                            Video
                          </Box>
                        ) : null}
                      </motion.div>
                    )}
                  </>
                )
              })}
            </GridItem>
          </Grid>
        </Box>
      )}
    </AnimatePresence>
  )
}

const MobileNav = () => {
  const [variant, setVariant] = useState('initial')

  useInterval(() => {
    setVariant('hide')
  }, 3000)

  return (
    <MotionBox
      variants={{ initial: { opacity: 1 }, hide: { opacity: 0 } }}
      animate={variant}
      display={['block', 'none']}
      pos="absolute"
      bottom="10px"
      w="100%"
      zIndex={1}
    >
      <Text
        opacity={0.5}
        textStyle="body"
        textTransform="uppercase"
        textAlign="center"
      >
        Swipe left or right to browse
      </Text>
    </MotionBox>
  )
}

// Utility function to generate Sanity video URL
const getSanityVideoUrl = (assetRef: string): string => {
  const cleanRef = assetRef
    .replace('file-', '')
    .replace('-mp4', '.mp4')
    .replace('-mov', '.mov')
    .replace('-avi', '.avi')
    .replace('-webm', '.webm')
  return `https://cdn.sanity.io/files/${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID}/${process.env.NEXT_PUBLIC_SANITY_PROJECT_DATASET}/${cleanRef}`
}

const SwiperVideo: FC<{
  url?: string
  videoAsset?: { _ref: string; _type: string }
}> = ({ url, videoAsset }) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [controlsVisibility, setControlsVisibility] = useState(true)

  // Determine the video URL based on the source
  const videoUrl = url || (videoAsset ? getSanityVideoUrl(videoAsset._ref) : '')

  const togglePlayPause = () => {
    try {
      if (isPlaying === false) {
        setIsPlaying(true)
        setControlsVisibility(false)
      } else {
        setIsPlaying(false)
        setControlsVisibility(true)
      }
    } catch (error) {
      // console.log(error)
    }
  }

  return (
    <Box pos="relative">
      <Box pointerEvents="none" w="100%" h="100%">
        <ReactPlayer
          url={videoUrl}
          width="100%"
          height="100%"
          playsinline
          playing={isPlaying}
        />
      </Box>
      <Box
        position="absolute"
        top={0}
        left={0}
        right={0}
        bottom={0}
        display="flex"
        alignItems="center"
        justifyContent="center"
        cursor="pointer"
        onClick={togglePlayPause}
        bg="transparent"
      >
        <MotionBox
          initial="visible"
          animate={controlsVisibility ? 'visible' : 'hidden'}
          variants={{
            hidden: {
              opacity: 0,
              transition: {
                duration: 0.5,
                ease: 'easeInOut',
              },
            },
            visible: {
              opacity: 1,
              transition: {
                duration: 0.5,
                ease: 'easeInOut',
              },
            },
          }}
          pointerEvents="none"
        >
          <Box
            w="60px"
            h="60px"
            display="flex"
            alignItems="center"
            justifyContent="center"
            bg="blackAlpha.600"
            borderRadius="full"
            backdropFilter="blur(4px)"
          >
            {isPlaying ? (
              <svg
                width="24"
                height="24"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="white"
              >
                <path d="M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm-1 17h-3v-10h3v10zm5 0h-3v-10h3v10z" />
              </svg>
            ) : (
              <svg
                width="24"
                height="24"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="white"
              >
                <path d="M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm-3 18v-12l10 6-10 6z" />
              </svg>
            )}
          </Box>
        </MotionBox>
      </Box>
    </Box>
  )
}

const SwiperActions: FC<{
  activeIndex: number
  project: Project
  setShowMasonry: (val: boolean) => void
}> = ({ activeIndex, project, setShowMasonry }) => {
  const {
    _id,
    gallery: images,
    title,
    categories,
    workType,
    monthYear,
  } = project
  const { mediaSelected } = useSnapshot(lightboxStore)

  return (
    <Box
      pos="absolute"
      top={[0, null, null, 'initial']}
      bottom={['initial', null, null, 0]}
      left={0}
      right={0}
      mx={8}
    >
      <Flex justifyContent="space-between">
        <Box
          w={['100%', null, null, '50%']}
          textAlign={['center', null, null, 'left']}
          zIndex={2}
        >
          <Button
            variant="link"
            textTransform="uppercase"
            color="blackAlpha.700"
            style={{ bottom: 'initial' }}
            onClick={() => {
              setShowMasonry(true)
            }}
          >
            <Text textStyle="body" className="custom-pagination" />
          </Button>
        </Box>
        {activeIndex != images.length &&
          activeIndex < images.length &&
          images[activeIndex]._type === 'photo' && (
            <Box
              w="50%"
              textAlign="right"
              zIndex={2}
              display={['none', null, null, 'block']}
            >
              <Button
                variant="link"
                textTransform="uppercase"
                color="blackAlpha.700"
                onClick={() => {
                  if (
                    _.find(mediaSelected, { _key: images[activeIndex]._key })
                  ) {
                    // lightboxStore.mediaSelected = mediaSelected.filter(
                    //   (image) => image.id !== images[activeIndex].id
                    // )
                    const index = mediaSelected.findIndex(
                      (image) => image.id == _id
                    )
                    lightboxStore.mediaSelected.splice(index, 1)
                  } else {
                    if (images[activeIndex]._type === 'photo') {
                      lightboxStore.mediaSelected.push({
                        ...(images[activeIndex] as ExtendedSanityImageObject),
                        title,
                        project: _id,
                        categories: categories,
                        monthYear: monthYear,
                        workType: workType,
                      })
                    }
                  }
                }}
              >
                <Text textStyle="body">
                  {_.find(mediaSelected, { _key: images[activeIndex]?._key })
                    ? 'Remove from lightbox — '
                    : 'Add to lightbox — '}
                  {`(${mediaSelected.length})`}
                </Text>
              </Button>
            </Box>
          )}
      </Flex>
    </Box>
  )
}

const SwiperNavigation: FC<{ activeIndex: number; totalSlide: number }> = ({
  activeIndex,
  totalSlide,
}) => {
  return (
    <>
      <IconButton
        display={['none', null, null, activeIndex == 0 ? 'none' : 'flex']}
        bg="transparent"
        aria-label="Previous"
        _hover={{}}
        _active={{}}
        icon={
          <svg
            width="15"
            height="28"
            viewBox="0 0 30 57"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M28.6504 0.477051L0.954102 28.1731L28.6504 55.8694"
              stroke="black"
            />
          </svg>
        }
        size="lg"
        className="custom-button-prev"
        pl="2rem"
        justifyContent="flex-start"
        pos="absolute"
        top={['initial', null, null, '50%']}
        bottom={[0, null, null, 'initial']}
        left={[4, null, null, 0]}
        width="30vw"
        height="80vh"
        transform="translateY(-50%)"
        zIndex={2}
      />
      <IconButton
        display={[
          'none',
          null,
          null,
          activeIndex == totalSlide ? 'none' : 'flex',
        ]}
        bg="transparent"
        aria-label="Next"
        _hover={{}}
        _active={{}}
        icon={
          <svg
            width="15"
            height="28"
            viewBox="0 0 29 57"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0.379883 55.8694L28.0762 28.1733L0.379883 0.477051"
              stroke="black"
            />
          </svg>
        }
        size="lg"
        className="custom-button-next"
        pr="2rem"
        justifyContent="flex-end"
        pos="absolute"
        top={['initial', null, null, '50%']}
        bottom={[0, null, null, 'initial']}
        right={[4, null, null, 0]}
        width="30vw"
        height="80vh"
        transform="translateY(-50%)"
        zIndex={2}
      />
    </>
  )
}

export default ProjectCarousel
