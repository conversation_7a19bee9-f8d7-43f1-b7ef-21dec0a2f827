import { AnimatePresence } from 'framer-motion'
import React, { FC, useMemo, useState } from 'react'
import NextLink from 'next/link'
import { Box, Grid, LinkBox, LinkOverlay, Text, VStack } from '@chakra-ui/react'

import { Project } from '@data'
import MotionBox from '@components/motion-box'
import Photo, { ExtendedSanityImageObject } from '@components/photo'
import { fadeAnim } from 'data/animation'

const ProjectCard: FC<{ project: Project }> = ({ project }) => {
  return (
    <MotionBox
      h="100%"
      initial="hide"
      animate="show"
      exit="hide"
      variants={fadeAnim}
      overflow="clip"
    >
      <ImageContainer
        slug={project.slug?.current!}
        featuredImage={project.featuredImage}
        hoverImages={project.hoverImages}
        project={project}
      />
    </MotionBox>
  )
}

const ImageContainer: FC<{
  project: Project
  slug: string
  featuredImage: ExtendedSanityImageObject
  hoverImages: ExtendedSanityImageObject[] | undefined
}> = ({ project, slug, featuredImage, hoverImages }) => {
  const [anim, setAnim] = useState('hide')
  const categories = useMemo(() => {
    return project.categories?.flatMap(
      (category, index) => `${index > 0 ? ',' : ''} ${category.name}`
    )
  }, [project])

  return (
    <LinkBox as="div" position="relative" height="100%">
      <AnimatePresence mode="wait" initial={false}>
        <MotionBox
          className="111"
          pos="relative"
          pt={5}
          px={5}
          h="100%"
          w="100%"
          overflowX="clip"
          cursor="pointer"
          onHoverStart={() => setAnim('show')}
          onHoverEnd={() => setAnim('hide')}
        >
          <NextLink href={`/works/${slug}`} passHref>
            <LinkOverlay>
              <MotionBox
                pos="relative"
                initial="hide"
                animate="show"
                exit="hide"
                className="333"
                variants={fadeAnim}
                h="100%"
                w="100%"
                display="block"
                textAlign="center"
              >
                <Box
                  display="inline-flex"
                  flexDir="column"
                  justifyContent="center"
                  maxW="100%"
                >
                  <Box
                    position="relative"
                    display="inline-flex"
                    height="23vw"
                    alignItems="flex-end"
                  >
                    <Photo
                      photo={featuredImage}
                      layout="fill"
                      objectPosition="bottom"
                      isThumbnail
                      galleryMode
                    />
                  </Box>

                  {hoverImages?.map((image, index) => {
                    return (
                      <Box pos="absolute" inset={1} key={index}>
                        <MotionBox
                          position="relative"
                          display="inline-flex"
                          height="23vw"
                          alignItems="flex-end"
                          w="100%"
                          initial={{
                            opacity: 0,
                            x: index == 0 ? '-10%' : '10%',
                            y: '0%',
                          }}
                          animate={
                            anim == 'show'
                              ? {
                                  opacity: 1,
                                  x: index == 0 ? '-10%' : '10%',
                                  y: index == 0 ? '-5%' : '-10%',
                                }
                              : {
                                  opacity: 0,
                                  x: index == 0 ? '-10%' : '10%',
                                  y: '0%',
                                }
                          }
                          // @ts-ignore
                          // transition={{ delay: 0.2 }}
                          exit={{ opacity: 0 }}
                          justifyContent="center"
                        >
                          <Photo
                            photo={image}
                            layout="fill"
                            objectPosition="bottom"
                            isThumbnail
                            galleryMode
                          />
                        </MotionBox>
                      </Box>
                    )
                  })}

                  <VStack
                    mt={3}
                    align={['center', null, null, 'start']}
                    spacing="0"
                  >
                    <Text
                      textStyle="body"
                      textAlign={['center', null, null, 'left']}
                    >
                      {project.title}
                    </Text>
                    <Text
                      textStyle="body"
                      opacity={0.5}
                      textAlign={['center', null, null, 'left']}
                      fontSize="10px"
                    >
                      {categories}
                    </Text>
                  </VStack>
                </Box>
              </MotionBox>
            </LinkOverlay>
          </NextLink>
        </MotionBox>
      </AnimatePresence>
    </LinkBox>
  )
}

export default ProjectCard
