import React, { FC, useMemo } from 'react'
import NextLink from 'next/link'
import { LinkBox, LinkOverlay, Text, VStack } from '@chakra-ui/react'

import { Project } from '@data'
import MotionBox from '@components/motion-box'
import Photo from '@components/photo'
import { fadeAnim } from 'data/animation'

const MasonryCard: FC<{ project: Project }> = ({ project }) => {
  const categories = useMemo(() => {
    return project.categories?.flatMap(
      (category, index) => `${index > 0 ? ',' : ''} ${category.name}`
    )
  }, [project])

  return (
    <MotionBox
      initial="hide"
      animate="show"
      exit="hide"
      variants={fadeAnim}
      w="100%"
      mb={8}
      display="inline-block"
    >
      <LinkBox as="div">
        <NextLink href={`/works/${project.slug?.current!}`} passHref>
          <LinkOverlay>
            <Photo photo={project.featuredImage} isThumbnail />
          </LinkOverlay>
        </NextLink>
      </LinkBox>
      <VStack align={['start', null, null, 'start']} spacing="0" mt={'10px'}>
        <Text textStyle="body" textAlign={['center', null, null, 'left']}>
          {project.title}
        </Text>
        <Text textStyle="body" opacity={0.5} fontSize="9px">
          {categories}
        </Text>
      </VStack>
    </MotionBox>
  )
}

export default MasonryCard
