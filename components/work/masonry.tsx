import { Box, Grid, GridItem } from '@chakra-ui/react'
import React, { FC, useMemo } from 'react'
import { AnimatePresence } from 'framer-motion'

import { Project } from '@data'
import MasonryCard from '@components/work/masonry-card'

const Masonry: FC<{ projects: Project[]; filters: string[] }> = ({
  projects,
  filters,
}) => {
  // TODO: sort or filter?
  const filteredProjects = useMemo(() => {
    if (filters.length <= 0) return projects

    return projects.filter((project) => {
      return project.categories.some((category) => {
        return filters.includes(category.value)
      })
    })
  }, [projects, filters])

  return (
    <AnimatePresence>
      {/* <Box w="100%" mx="auto" sx={{ columnCount: 2, columnGap: 4 }}>
        {filteredProjects.map((project) => {
          return <MasonryCard key={project._id} project={project} />
        })}
      </Box> */}
      <Grid templateColumns="repeat(2, 1fr)" gap={4}>
        <GridItem>
          {filteredProjects.map((project, index) => {
            return (
              <>
                {index % 2 == 0 && (
                  <MasonryCard key={project._id} project={project} />
                )}
              </>
            )
          })}
        </GridItem>

        <GridItem>
          {filteredProjects.map((project, index) => {
            return (
              <>
                {index % 2 == 1 && (
                  <MasonryCard key={project._id} project={project} />
                )}
              </>
            )
          })}
        </GridItem>
      </Grid>
    </AnimatePresence>
  )
}

export default Masonry
