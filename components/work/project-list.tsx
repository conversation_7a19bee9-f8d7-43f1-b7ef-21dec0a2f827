import React, { FC, useMemo } from 'react'
import { AnimatePresence } from 'framer-motion'
import { Box, Grid, GridItem, Text, VStack, Wrap } from '@chakra-ui/react'

import ProjectCard from '@components/work/project-card'
import { Project } from '@data'

const ProjectList: FC<{ projects: Project[]; filters: string[] }> = ({
  projects,
  filters,
}) => {
  // TODO: sort or filter?
  const filteredProjects = useMemo(() => {
    if (filters.length <= 0) return projects

    return projects.filter((project) => {
      return project.categories.some((category) => {
        return filters.includes(category.value)
      })
    })
  }, [projects, filters])

  return (
    <AnimatePresence>
      <Grid
        gap={4}
        templateColumns="repeat(4, 1fr)"
        // gridAutoRows="calc(((100vh - 28px) / 12) * 5)"
      >
        {filteredProjects.map((project) => {
          return (
            <GridItem key={project._id} colSpan={[2, null, null, 1]}>
              <ProjectCard project={project} />
            </GridItem>
          )
        })}
      </Grid>
    </AnimatePresence>
  )
}

export default ProjectList
