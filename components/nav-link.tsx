import { Link as ChakraLink, LinkProps, Text } from '@chakra-ui/react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import React from 'react'

interface NavLinkProps extends LinkProps {
  children?: string | React.ReactNode
  to: string
  activeProps?: LinkProps
  _hover?: LinkProps
}

function NavLink({
  to,
  activeProps,
  children,
  _hover,
  color,
  ...props
}: NavLinkProps) {
  const router = useRouter()
  const isActive = router.pathname === to

  return (
    <Link href={to} passHref>
      <ChakraLink {...props} {...activeProps} color={color}>
        {children}
      </ChakraLink>
    </Link>
  )
}

export default NavLink
