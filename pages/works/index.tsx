import {
  Box,
  <PERSON>lex,
  H<PERSON>tack,
  I<PERSON><PERSON><PERSON><PERSON>,
  Link,
  Text,
  VStack,
  Wrap,
} from '@chakra-ui/react'
import React, { type FC, useEffect, useRef, useState } from 'react'
import type { GetStaticPropsContext, NextPage } from 'next'
import { AnimatePresence, motion, useScroll } from 'framer-motion'
import type { ParsedUrlQuery } from 'querystring'

import {
  getAllProjects,
  getProjectsNumber,
  getStaticPage,
  type Project,
} from '@data'
import MotionBox from '@components/motion-box'
import Masonry from '@components/work/masonry'
import ProjectList from '@components/work/project-list'
import WorkTransition from 'layouts/work_transition'
import CommonLayout from 'layouts/common'
import type { WorkCategory } from 'studio/schema'
import { useSnapshot } from 'valtio'
import pageStore from 'data/page'
import _ from 'lodash'
import InfiniteScroll from 'react-infinite-scroller'
import { getSeoSettings, getGeneralSettings } from 'data/seo'
import SEO from '@components/seo'
import { withLockdownCheck } from '@lib/with-lockdown-check'

const WorksPage: NextPage<Props> = ({
  data,
  total,
  categoryData,
  defaultSEO,
}) => {
  const { pageVisited } = useSnapshot(pageStore)
  const [loading, setLoading] = useState(!_.find(pageVisited, { page: 'work' }))

  useEffect(() => {
    setTimeout(() => {
      setLoading(false)
    }, 1000)
  }, [])

  useEffect(() => {
    if (!_.find(pageVisited, { page: 'work' }) && !loading) {
      pageStore.pageVisited.push({ page: 'work' })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading])

  return (
    <CommonLayout>
      <SEO
        title={categoryData?.seo?.metaTitle}
        description={categoryData?.seo?.metaDesc}
        image={
          categoryData?.seo?.shareGraphic?.asset?._ref
            ? `https://cdn.sanity.io/images/${
                process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'undefined'
              }/${
                process.env.NEXT_PUBLIC_SANITY_PROJECT_DATASET || 'production'
              }/${categoryData.seo.shareGraphic.asset._ref.split('-')[1]}.${
                categoryData.seo.shareGraphic.asset._ref.split('-')[2] ===
                'jpeg'
                  ? 'jpg'
                  : categoryData.seo.shareGraphic.asset._ref.split('-')[2]
              }`
            : undefined
        }
        defaultSEO={defaultSEO}
      />
      {/* first time */}
      {!_.find(pageVisited, { page: 'work' }) ? (
        <AnimatePresence
          initial={false}
          onExitComplete={() => window.scrollTo(0, 0)}
        >
          {!loading && (
            <MotionBox key="work-content">
              <WorkContent
                projects={data}
                categories={categoryData.filterCategory}
                total={total}
              />
            </MotionBox>
          )}
          {loading && !_.find(pageVisited, { page: 'work' }) && (
            <WorkTransition />
          )}
        </AnimatePresence>
      ) : (
        <WorkContent
          projects={data}
          categories={categoryData.filterCategory}
          total={total}
        />
      )}
    </CommonLayout>
  )
}

const WorkContent: FC<{
  projects: Project[]
  categories: WorkCategory[]
  total: number
}> = ({ projects, categories, total }) => {
  const [filters, setFilters] = useState<string[]>([])
  const [projectList, setProjectList] = useState(projects)
  const hasMore = projectList.length < total

  const fetchMoreData = async () => {
    const result = await getAllProjects(
      'order(monthYear desc)',
      projectList.length
    )

    if (result.length > 0) {
      setProjectList((e) => e.concat(result))
    }
  }

  return (
    <>
      <Box
        pos={['relative', null, null, 'fixed']}
        top={['initial', null, null, '0']}
        left={['initial', null, null, '0']}
        right={['initial', null, null, '0']}
        mx={[4, null, null, 8]}
        pt={[120, null, null, 7]}
        pb={[16, null, null, 7]}
        height={['auto', null, null, 'calc(((100vh - 28px) / 12) * 3 + 58px)']}
        display="flex"
        alignItems="center"
        justifyContent="center"
        bg="white"
      >
        <Filters {...{ categories, filters, setFilters }} />
      </Box>

      <InfiniteScroll
        // dataLength={projectsList.length}
        // next={fetchMoreData}
        pageStart={1}
        loadMore={fetchMoreData}
        hasMore={hasMore}
        initialLoad={false}
        // loader={
        //   <Box
        //     pos="absolute"
        //     bottom={2}
        //     left="50%"
        //     transform="translateX(-50%)"
        //   >
        //     <Spinner />
        //   </Box>
        // }
      >
        <Box display={['block', null, null, 'none']} mx={4}>
          <Masonry projects={projectList} filters={filters} />
        </Box>

        <Box
          display={['none', null, null, 'block']}
          mx={8}
          mt="calc(((100vh - 28px) / 12) * 3 + 40px)"
        >
          <ProjectList projects={projectList} filters={filters} />
        </Box>
      </InfiniteScroll>

      <Box
        pos="fixed"
        bottom={0}
        left={0}
        right={0}
        pb={12}
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <BackToTopButton />
      </Box>

      <Box py={5} bottom={0} w="full" bg="white" px={12}>
        <Flex justifyContent="space-between">
          <HStack spacing={30}>
            <Link
              href="https://www.instagram.com/studio.oooze/"
              isExternal
              zIndex={999}
            >
              <Text textStyle="body" fontWeight={500}>
                Instagram
              </Text>
            </Link>

            <Link href="https://vimeo.com/oooze" isExternal zIndex={999}>
              <Text textStyle="body" fontWeight={500}>
                Vimeo
              </Text>
            </Link>
          </HStack>

          <HStack spacing={30}>
            <Link href="/about" zIndex={999}>
              <Text textStyle="body" fontWeight={500}>
                CONTACT US
              </Text>
            </Link>
          </HStack>
        </Flex>
      </Box>
    </>
  )
}

const Filters: FC<{
  categories: WorkCategory[]
  filters: string[]
  setFilters: React.Dispatch<React.SetStateAction<string[]>>
}> = ({ categories, filters, setFilters }) => {
  const categoryChunks = _.chunk(categories, 3)
  const { scrollY } = useScroll()
  const [hidden, setHidden] = React.useState(false)

  function update() {
    if (scrollY.get() < 200 && scrollY.get() < scrollY.getPrevious()) {
      setHidden(false)
    } else if (scrollY.get() > 200 && scrollY.get() > scrollY.getPrevious()) {
      setHidden(true)
    }
  }

  useEffect(() => {
    return scrollY.on('change', () => update())
  })

  return (
    <VStack
      w="full"
      as={MotionBox}
      animate={{
        opacity: hidden ? 0 : 1,
      }}
      // @ts-ignore
      transition={{ ease: [0.1, 0.25, 0.3, 1], duration: 0.6 }}
    >
      <Text textStyle="body">Sort by</Text>
      <MotionBox
        initial="preparing"
        viewport={{ once: true }}
        whileInView="showing"
        variants={{
          preparing: {
            opacity: 0,
            transition: {
              staggerChildren: 0.2,
            },
          },
          showing: {
            opacity: 1,
            transition: {
              delay: 0.2,
              staggerChildren: 0.6,
            },
          },
        }}
      >
        {categoryChunks.map((chunks, index) => {
          return (
            <Wrap
              key={index}
              flexWrap="wrap"
              spacingX={8}
              spacingY={1}
              justify="center"
              w="100%"
              as={MotionBox}
              variants={{
                preparing: {
                  opacity: 0,
                  transition: {
                    staggerChildren: 0.2,
                  },
                },
                showing: {
                  opacity: 1,
                  transition: {
                    staggerChildren: 0.2,
                  },
                },
              }}
            >
              {chunks.map((category) => {
                return (
                  <MotionBox
                    key={category.value}
                    variants={{
                      preparing: {
                        opacity: 0,
                      },
                      showing: {
                        opacity: 1,
                      },
                    }}
                  >
                    <Text
                      as={motion.p}
                      textStyle="h2"
                      textTransform="capitalize"
                      cursor="pointer"
                      animate={{
                        opacity: filters.includes(category.value) ? 1 : 0.5,
                      }}
                      whileHover={{ color: 'black', opacity: 1 }}
                      onClick={() => {
                        if (filters.includes(category.value)) {
                          setFilters(
                            filters.filter(
                              (filter) => filter !== category.value
                            )
                          )
                        } else {
                          setFilters([...filters, category.value])
                        }
                      }}
                    >
                      {category.name}
                    </Text>
                  </MotionBox>
                )
              })}
            </Wrap>
          )
        })}
      </MotionBox>
    </VStack>
  )
}

const BackToTopButton = () => {
  const { scrollY } = useScroll()
  const [hidden, setHidden] = React.useState(false)

  function update() {
    if (scrollY.get() > 200) {
      setHidden(false)
    } else {
      setHidden(true)
    }
  }

  useEffect(() => {
    return scrollY.on('change', () => update())
  })

  const handleBackToTopClick = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <MotionBox
      animate={{
        opacity: hidden ? 0 : 1,
      }}
      // @ts-ignore
      transition={{ ease: [0.1, 0.25, 0.3, 1], duration: 0.6 }}
    >
      <IconButton
        aria-label="backToTop"
        background="transparent"
        icon={
          <svg
            width="15"
            height="28"
            viewBox="0 0 29 57"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            transform="rotate(90)"
          >
            <path
              d="M28.6504 0.477051L0.954102 28.1731L28.6504 55.8694"
              stroke="black"
            />
          </svg>
        }
        onClick={handleBackToTopClick}
      />
    </MotionBox>
  )
}

type UnwrapPromise<T> = T extends Promise<infer U> ? U : T
type Props = UnwrapPromise<ReturnType<typeof getStaticProps>>['props']

export async function getStaticProps({
  preview,
  previewData,
}: GetStaticPropsContext<ParsedUrlQuery, { token: string }>) {
  const pageData = await getAllProjects('order(monthYear desc)')
  const total = await getProjectsNumber()
  const categoryData = await getStaticPage(
    `type == 'work-page' && slug.current == 'work'`
  )

  // Get global SEO settings
  const defaultSEO = await getSeoSettings()
  const siteSettings = await getGeneralSettings()

  return {
    props: {
      data: pageData,
      total: total,
      categoryData,
      defaultSEO,
      siteSettings,
    },
    revalidate: 300,
  }
}

export default withLockdownCheck(WorksPage)
