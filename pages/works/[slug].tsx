import React from 'react'
import { Box } from '@chakra-ui/react'
import type { GetStaticPropsContext, NextPage } from 'next'
import type { ParsedUrlQuery } from 'querystring'

import { getAllDocSlugs, getProject } from '@data'
import CommonLayout from 'layouts/common'
import NextError from 'next/error'
import ProjectCarousel from '@components/work/project-carousel'
import { getSeoSettings, getGeneralSettings } from 'data/seo'
import SEO from '@components/seo'
import { withLockdownCheck } from '@lib/with-lockdown-check'

const ProjectPage: NextPage<Props> = ({ data, defaultSEO }) => {
  if (!data) {
    // TODO:
    return <NextError statusCode={404} />
  }

  return (
    <CommonLayout>
      <SEO
        title={data?.current.seo?.metaTitle ?? data.current.title}
        description={data?.current.seo?.metaDesc}
        image={
          data?.current.seo?.shareGraphic?.asset?._ref
            ? `https://cdn.sanity.io/images/${
                process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'undefined'
              }/${
                process.env.NEXT_PUBLIC_SANITY_PROJECT_DATASET || 'production'
              }/${data.current.seo.shareGraphic.asset._ref.split('-')[1]}.${
                data.current.seo.shareGraphic.asset._ref.split('-')[2] ===
                'jpeg'
                  ? 'jpg'
                  : data.current.seo.shareGraphic.asset._ref.split('-')[2]
              }`
            : undefined
        }
        defaultSEO={defaultSEO}
      />
      <Box
        h="var(--chakra-vh)"
        w="100vw"
        pt={[20, null, null, 7]}
        pb={[10, null, null, 7]}
      >
        <ProjectCarousel
          project={data.current}
          next={data.next?.slug}
          previous={data.previous?.slug}
        />
      </Box>
    </CommonLayout>
  )
}

type UnwrapPromise<T> = T extends Promise<infer U> ? U : T
type Props = UnwrapPromise<ReturnType<typeof getStaticProps>>['props']
interface IParams extends ParsedUrlQuery {
  slug: string
}

export async function getStaticProps({
  params,
  preview,
  previewData,
}: GetStaticPropsContext<IParams, { token: string }>) {
  if (!params?.slug) {
    return {
      props: {
        data: null,
      },
      revalidate: 300,
    }
  }

  const pageData = await getProject(params!.slug)

  // Get global SEO settings
  const defaultSEO = await getSeoSettings()
  const siteSettings = await getGeneralSettings()

  return {
    props: {
      data: pageData,
      defaultSEO,
      siteSettings,
    },
    revalidate: 300,
  }
}

export async function getStaticPaths() {
  const docs = await getAllDocSlugs('work')

  return {
    paths:
      docs?.map((doc) => {
        return {
          params: { slug: doc.slug },
        }
      }) || [],
    fallback: 'blocking',
  }
}

export default withLockdownCheck(ProjectPage)
