import { Box, Flex, Grid, GridItem } from '@chakra-ui/react'
import { AnimatePresence } from 'framer-motion'
import type { GetStaticPropsContext, NextPage } from 'next'
import type { ParsedUrlQuery } from 'querystring'
import React, { type FC, useEffect, useState } from 'react'
import ReactPlayer from 'react-player'

import CommonLayout from 'layouts/common'
import ProductionTransition from 'layouts/production_transition'
import { getStaticPage, type PageResult } from '@data'
import MotionBox from '@components/motion-box'
import Content from '@components/block-content'
import Photo from '@components/photo'
import pageStore from 'data/page'
import { useSnapshot } from 'valtio'
import _ from 'lodash'
import { getSeoSettings, getGeneralSettings } from 'data/seo'
import SEO from '@components/seo'
import LockdownLayout from '@components/lockdown-layout'

const ProductionPage: NextPage<Props> = ({ data, defaultSEO, siteSettings }) => {
  const [loading, setLoading] = useState(true)
  const { pageVisited } = useSnapshot(pageStore)

  useEffect(() => {
    setTimeout(() => {
      setLoading(false)
    }, 1000)
  }, [])

  useEffect(() => {
    if (!_.find(pageVisited, { page: 'production' }) && !loading) {
      pageStore.pageVisited.push({ page: 'production' })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading])

  // Check if lockdown mode is enabled
  if (siteSettings?.lockdown) {
    return (
      <LockdownLayout siteTitle={siteSettings.siteTitle}>
        {/* You can add any additional content here if needed */}
      </LockdownLayout>
    )
  }

  return (
    <CommonLayout bg="brand.gray.200">
      <SEO
        title={data?.seo?.metaTitle}
        description={data?.seo?.metaDesc}
        image={
          data?.seo?.shareGraphic?.asset?._ref
            ? `https://cdn.sanity.io/images/${
                process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'undefined'
              }/${
                process.env.NEXT_PUBLIC_SANITY_PROJECT_DATASET || 'production'
              }/${data.seo.shareGraphic.asset._ref.split('-')[1]}.${
                data.seo.shareGraphic.asset._ref.split('-')[2] === 'jpeg'
                  ? 'jpg'
                  : data.seo.shareGraphic.asset._ref.split('-')[2]
              }`
            : undefined
        }
        defaultSEO={defaultSEO}
      />
      <AnimatePresence
        initial={false}
        onExitComplete={() => window.scrollTo(0, 0)}
      >
        {!loading && (
          <MotionBox key="work-content">
            <ProductionContent page={data!} />
          </MotionBox>
        )}
        {loading && !_.find(pageVisited, { page: 'production' }) && (
          <ProductionTransition />
        )}
      </AnimatePresence>
    </CommonLayout>
  )
}

const ProductionContent: FC<{ page: PageResult }> = ({ page }) => {
  return (
    <Box px={[4, null, null, 8]} py={[0, null, null, 7]}>
      <Grid
        pt={[120, null, null, 0]}
        pb={[9, null, null, 0]}
        templateColumns={['repeat(4, 1fr)', null, null, 'repeat(12, 1fr)']}
        columnGap={4}
        rowGap={[12, null, null, 4]}
        // minH="calc(100vh - 56px)"
      >
        <GridItem colStart={[1, null, null, 5]} colSpan={[4, null, null, 8]}>
          <Box w="100%">
            <Grid
              templateColumns={['repeat(4, 1fr)', null, null, 'repeat(8, 1fr)']}
              gridAutoRows={[
                'calc(((100vh - 28px) / 6))',
                null,
                null,
                'calc(((100vh - 28px) / 12))',
              ]}
              columnGap={4}
              rowGap={[12, null, null, 4]}
            >
              {page.productionContents?.map((content, index) => {
                if (index == 0) {
                  return (
                    <GridItem
                      key={content._key}
                      colStart={[1, null, null, 1]}
                      colSpan={[3, null, null, 4]}
                      rowStart={[1, null, null, 2]}
                      rowSpan={[3, null, null, 7]}
                    >
                      <Flex h="100%" flexDir="column">
                        <Box flex={5} height={[0, 0, 0, '100%']}>
                          <Box pos="relative" h="100%">
                            {content.mediaType === 'image' && content.image && (
                              <Photo
                                photo={content.image}
                                layout="fill"
                                objectPosition="bottom"
                              />
                            )}
                            {content.mediaType === 'video' && content.video && (
                              <ReactPlayer
                                url={`https://cdn.sanity.io/files/${
                                  process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
                                }/${
                                  process.env.NEXT_PUBLIC_SANITY_PROJECT_DATASET
                                }/${content.video?.asset?._ref
                                  ?.replace('file-', '')
                                  ?.replace('-mp4', '.mp4')}`}
                                width="100%"
                                height="100%"
                                muted
                                playing
                                loop
                                playsinline
                              />
                            )}
                          </Box>
                        </Box>
                        <Box flex={2} pt={4} textAlign="center">
                          <Grid
                            templateColumns={[
                              '1',
                              null,
                              null,
                              'repeat(4, 1fr)',
                            ]}
                            gap={4}
                          >
                            <GridItem
                              colStart={[1, null, null, 2]}
                              colSpan={[1, null, null, 2]}
                            >
                              <Content blocks={content.content} />
                            </GridItem>
                          </Grid>
                        </Box>
                      </Flex>
                    </GridItem>
                  )
                }

                if (index == 1) {
                  return (
                    <GridItem
                      key={content._key}
                      colStart={[2, null, null, 5]}
                      colSpan={[3, null, null, 4]}
                      rowStart={[4, null, null, 3]}
                      rowSpan={[3, null, null, 7]}
                    >
                      <Flex h="100%" flexDir="column">
                        <Box flex={5} height={[0, 0, 0, '100%']}>
                          <Box pos="relative" h="100%">
                            {content.mediaType === 'image' && content.image && (
                              <Photo
                                photo={content.image}
                                layout="fill"
                                objectPosition="bottom"
                              />
                            )}
                            {content.mediaType === 'video' && content.video && (
                              <ReactPlayer
                                url={`https://cdn.sanity.io/files/${
                                  process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
                                }/${
                                  process.env.NEXT_PUBLIC_SANITY_PROJECT_DATASET
                                }/${content.video?.asset?._ref
                                  ?.replace('file-', '')
                                  ?.replace('-mp4', '.mp4')}`}
                                width="100%"
                                height="100%"
                                muted
                                playing
                                loop
                                playsinline
                              />
                            )}
                          </Box>
                        </Box>
                        <Box flex={2} pt={4} textAlign="center">
                          <Grid
                            templateColumns={[
                              '1',
                              null,
                              null,
                              'repeat(4, 1fr)',
                            ]}
                            gap={4}
                          >
                            <GridItem
                              colStart={[1, null, null, 2]}
                              colSpan={[1, null, null, 2]}
                            >
                              <Content blocks={content.content} />
                            </GridItem>
                          </Grid>
                        </Box>
                      </Flex>
                    </GridItem>
                  )
                }

                return (
                  <GridItem
                    key={content._key}
                    colStart={[1, null, null, 1]}
                    colSpan={[4, null, null, 8]}
                    rowStart={[7, null, null, 10]}
                    rowSpan={[3, null, null, 7]}
                  >
                    <Flex h="100%" flexDir="column">
                      <Box flex={5} height={[0, 0, 0, '100%']}>
                        <Box pos="relative" h="100%">
                          {content.mediaType === 'image' && content.image && (
                            <Photo
                              photo={content.image}
                              layout="fill"
                              objectPosition="bottom"
                            />
                          )}
                          {content.mediaType === 'video' && content.video && (
                            <ReactPlayer
                              url={`https://cdn.sanity.io/files/${
                                process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
                              }/${
                                process.env.NEXT_PUBLIC_SANITY_PROJECT_DATASET
                              }/${content.video?.asset?._ref
                                ?.replace('file-', '')
                                ?.replace('-mp4', '.mp4')}`}
                              width="100%"
                              height="100%"
                              muted
                              playing
                              loop
                              playsinline
                            />
                          )}
                        </Box>
                      </Box>
                      <Box flex={2} pt={4} textAlign="center">
                        <Grid
                          templateColumns={[
                            'repeat(4, 1fr)',
                            null,
                            null,
                            'repeat(8, 1fr)',
                          ]}
                          gap={4}
                        >
                          <GridItem
                            colStart={[2, null, null, 3]}
                            colSpan={[2, null, null, 4]}
                          >
                            <Content blocks={content.content} />
                          </GridItem>
                        </Grid>
                      </Box>
                    </Flex>
                    <Box pt={7} />
                  </GridItem>
                )
              })}
            </Grid>
          </Box>
        </GridItem>

        <GridItem colStart={1} colSpan={4} rowStart={1}>
          <Box
            display={['none', 'none', 'none', 'block']}
            pos={'fixed'}
            bottom={7}
            left={8}
            width={'calc((100vw - 28px) / 12 * 4 - 24px)'}
          >
            <Box
              sx={{
                letterSpacing: ['0.01em', '0'],
                opacity: [1, 0.7],
              }}
            >
              <Content blocks={page.description} />
            </Box>
          </Box>
          <Box
            display={['block', 'block', 'block', 'none']}
            pos={'relative'}
            bottom={'initial'}
            left={'initial'}
            width={'100%'}
          >
            <Box
              sx={{
                letterSpacing: ['0.01em', '0'],
                opacity: [1, 0.7],
              }}
            >
              <Content blocks={page.descriptionMobile} />
            </Box>
          </Box>
        </GridItem>
      </Grid>
    </Box>
  )
}

type UnwrapPromise<T> = T extends Promise<infer U> ? U : T
type Props = UnwrapPromise<ReturnType<typeof getStaticProps>>['props']

export async function getStaticProps({
  preview,
  previewData,
}: GetStaticPropsContext<ParsedUrlQuery, { token: string }>) {
  const pageData = await getStaticPage(
    `type == 'production-page' && slug.current == 'production'`
  )

  // Get global SEO settings
  const defaultSEO = await getSeoSettings()
  const siteSettings = await getGeneralSettings()

  return {
    props: {
      data: pageData,
      defaultSEO,
      siteSettings,
    },
    revalidate: 300,
  }
}

export default ProductionPage
