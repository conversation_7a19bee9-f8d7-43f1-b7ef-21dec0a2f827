// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import axios from 'axios'
import type { NextApiRequest, NextApiResponse } from 'next'
import imageUrlBuilder from '@sanity/image-url'
import { rawSanityClient } from '@lib/sanity'
import { ExtendedSanityImageObject } from '@components/photo'

type BodyData = {
  images: ExtendedSanityImageObject[]
}

export type MyCustomRequest = Omit<NextApiRequest, 'body'> & { body: BodyData }

type Data = {
  success: boolean
  data?: any
  message?: string
}

export default async function handler(
  req: MyCustomRequest,
  res: NextApiResponse<Data>
) {
  const builder = imageUrlBuilder(rawSanityClient)

  const urlFor = (source: ExtendedSanityImageObject) => {
    return builder
      .image(source)
      .auto('format')
      .fit('max')
      .width(1280)
      .toString()
  }

  if (req.method !== 'POST') {
    res
      .status(405)
      .json({ success: false, message: 'Only POST requests allowed' })
    return
  }

  const buffers: <PERSON>uffer[] = await Promise.all(
    req.body.images.map(async (element) => {
      let url = urlFor(element)
      return await (
        await axios.get(url, { responseType: 'arraybuffer' })
      ).data
    })
  )

  res.status(200).json({ success: true, data: buffers })
}
