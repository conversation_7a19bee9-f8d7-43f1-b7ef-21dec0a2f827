import React, { useEffect, useState } from 'react'
import { AnimatePresence } from 'framer-motion'

import type { GetStaticPropsContext, NextPage } from 'next'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import _ from 'lodash'
import shortUuid from 'short-uuid'

import MotionBox from '@components/motion-box'
import CommonLayout from 'layouts/common'
import LightboxTransition from 'layouts/lightbox_transition'
import { decodeShare } from '@lib/utility'
import { getProjectsById } from '@data'
import lightboxStore, { type LightboxImage } from 'data/store'
import pageStore from 'data/page'
import { useSnapshot } from 'valtio'
import { getSeoSettings, getGeneralSettings } from 'data/seo'
import { ParsedUrlQuery } from 'querystring'
import SEO from '@components/seo'
import LockdownLayout from '@components/lockdown-layout'

const LightboxContent = dynamic(
  () => import('@components/lightbox/lightbox-content'),
  { ssr: false }
)

const LightBoxPage: NextPage<Props> = ({ defaultSEO, siteSettings }) => {
  const [loading, setLoading] = useState(true)
  const [fetching, setFetching] = useState(false)
  const [hasFetch, setHasFetch] = useState(false)
  const page = useRouter().query
  const { pageVisited } = useSnapshot(pageStore)

  useEffect(() => {
    setTimeout(() => {
      // already fetching base64 here
      setLoading(false)
    }, 1000)
  }, [])

  // if has base64, fetch media
  const fetchImageProject = async (base64: string) => {
    const buffer = Buffer.from(base64, 'base64')
    const items = await decodeShare(buffer)

    const p = items.s.map((share) => {
      return share.p
    })

    const i = items.s.map((share) => {
      return share.i
    })

    const translator = shortUuid()
    const projects = _.uniq(p).map((id) => translator.toUUID(id))
    const response = await getProjectsById(projects)

    // filter out unneeded images
    const lightboxImage: LightboxImage[] = []
    for (const project of response) {
      for (const photo of project.gallery) {
        if (_.includes(i, photo._key)) {
          lightboxImage.push({
            ...photo,
            title: project.title,
            project: project._id,
          })
        }
      }
    }

    lightboxStore.mediaSelected = lightboxImage
    setFetching(false)
  }

  useEffect(() => {
    const base64 = page?.s
    if (base64 && !hasFetch) {
      setHasFetch(true)
      setFetching(true)
      fetchImageProject(base64 as string)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page])

  useEffect(() => {
    if (!_.find(pageVisited, { page: 'lightbox' }) && !loading) {
      pageStore.pageVisited.push({ page: 'lightbox' })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading])

  // Check if lockdown mode is enabled
  if (siteSettings?.lockdown) {
    return (
      <LockdownLayout siteTitle={siteSettings.siteTitle}>
        {/* You can add any additional content here if needed */}
      </LockdownLayout>
    )
  }

  // TODO: do not animate on ssr
  return (
    <CommonLayout bg="brand.gray.500" theme={'dark'}>
      <SEO defaultSEO={defaultSEO} />
      <AnimatePresence
        initial={false}
        onExitComplete={() => window.scrollTo(0, 0)}
      >
        {!loading && (
          <MotionBox key="work-content">
            <LightboxContent fetching={fetching} />
          </MotionBox>
        )}
        {loading && !_.find(pageVisited, { page: 'lightbox' }) && (
          <LightboxTransition />
        )}
      </AnimatePresence>
    </CommonLayout>
  )
}

type UnwrapPromise<T> = T extends Promise<infer U> ? U : T
type Props = UnwrapPromise<ReturnType<typeof getStaticProps>>['props']

export async function getStaticProps({
  preview,
  previewData,
}: GetStaticPropsContext<ParsedUrlQuery, { token: string }>) {
  // Get global SEO settings
  const defaultSEO = await getSeoSettings()
  const siteSettings = await getGeneralSettings()

  return {
    props: {
      defaultSEO,
      siteSettings,
    },
    revalidate: 300,
  }
}

export default LightBoxPage
