import {
  Grid,
  Box,
  useBreakpointValue,
  LinkBox,
  LinkOverlay,
  Text,
} from '@chakra-ui/react'
import type { GetStaticPropsContext, NextPage } from 'next'
import Link from 'next/link'

import CommonLayout from 'layouts/common'
import { type FC, useState } from 'react'
import type { ParsedUrlQuery } from 'querystring'
import { getStaticPage } from '@data'
import Photo, {
  type ExtendedSanityImageObject,
  type ImgElementStyle,
} from '@components/photo'
import Slider from '@components/home/<USER>'
import Content from '@components/block-content'
import SEO from '@components/seo'
import { getSeoSettings, getGeneralSettings } from 'data/seo'
import LockdownLayout from '@components/lockdown-layout'

const Home: NextPage<Props> = ({ data, defaultSEO, siteSettings }) => {
  const [currentColor, setCurrentColor] = useState('dark')

  // Check if lockdown mode is enabled
  if (siteSettings?.lockdown) {
    return (
      <LockdownLayout siteTitle={siteSettings.siteTitle}>
        {/* You can add any additional content here if needed */}
      </LockdownLayout>
    )
  }

  return (
    <CommonLayout theme={currentColor}>
      <SEO
        title={data?.seo?.metaTitle}
        description={data?.seo?.metaDesc}
        image={
          data?.seo?.shareGraphic?.asset?._ref
            ? `https://cdn.sanity.io/images/${
                process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'undefined'
              }/${
                process.env.NEXT_PUBLIC_SANITY_PROJECT_DATASET || 'production'
              }/${data.seo.shareGraphic.asset._ref.split('-')[1]}.${
                data.seo.shareGraphic.asset._ref.split('-')[2] === 'jpeg'
                  ? 'jpg'
                  : data.seo.shareGraphic.asset._ref.split('-')[2]
              }`
            : undefined
        }
        defaultSEO={defaultSEO}
        siteTitle={siteSettings?.siteTitle}
      />
      <Box bg="brand.gray.200">
        {data.homeContents && (
          <Slider slides={data.homeContents}>
            {data.landingImage && (
              <LandingImage
                landing={data.landingWork}
                title={data.landingImage.title}
                image={data.landingImage.image}
              />
            )}
          </Slider>
        )}
      </Box>
    </CommonLayout>
  )
}

const LandingImage: FC<{
  title: any
  image?: ExtendedSanityImageObject
  landing: any
}> = ({ title, image, landing }) => {
  // const color = useColorModeValue('white', 'black')
  const fit: ImgElementStyle['objectFit'] = useBreakpointValue({
    base: 'cover',
    sm: 'cover',
  })
  const position: ImgElementStyle['objectPosition'] = useBreakpointValue({
    base: 'bottom',
    sm: 'center',
  })

  return (
    <>
      <Box
        height={['30vh', '100vh']}
        pos="relative"
        mx={[5, 0]}
        mt={['100px', 0]}
        mb={[14, 0]}
      >
        <LinkBox pos="relative" w="100%" h="100%">
          <Link
            href={`/works/${landing?.gallery?.slug?.current ?? ''}`}
            passHref
          >
            <LinkOverlay>
              {image && <Photo photo={image} layout="fill" objectFit="cover" />}
            </LinkOverlay>
          </Link>
        </LinkBox>
        <Grid
          display={['none', 'block']}
          pos="absolute"
          bottom={0}
          left={0}
          right={0}
          mx={8}
          my={7}
          textColor="white"
        >
          <Box>
            {/* <Content blocks={title} /> */}
            <Text textStyle="p">{landing?.gallery?.title}</Text>
            <Text textStyle="p" opacity="0.5">
              {landing?.gallery?.categories?.flatMap(
                (category: any, index: number) =>
                  `${index > 0 ? ',' : ''} ${category.name}`
              )}
            </Text>
          </Box>
        </Grid>
      </Box>
    </>
  )
}

type UnwrapPromise<T> = T extends Promise<infer U> ? U : T
type Props = UnwrapPromise<ReturnType<typeof getStaticProps>>['props']

export async function getStaticProps({
  preview,
  previewData,
}: GetStaticPropsContext<ParsedUrlQuery, { token: string }>) {
  const pageData = await getStaticPage(
    `type == 'home-page' && slug.current == 'home'`
  )

  // Get global SEO settings
  const defaultSEO = await getSeoSettings()
  const siteSettings = await getGeneralSettings()

  return {
    props: {
      data: pageData,
      defaultSEO,
      siteSettings,
    },
    revalidate: 300,
  }
}

export default Home
