import type { GetStaticPropsContext, NextPage } from 'next'
import type { ParsedUrlQuery } from 'querystring'
import React, { type FC, useEffect, useState } from 'react'
import {
  Box,
  Grid,
  GridItem,
  List,
  ListItem,
  Text,
  VStack,
  Link,
} from '@chakra-ui/react'
import MotionBox from '@components/motion-box'
import { AnimatePresence } from 'framer-motion'

import AboutTransition from 'layouts/about_transition'
import CommonLayout from 'layouts/common'
import { getStaticPage, type PageResult } from '@data'
import Content from '@components/block-content'
import { typedSanityClient } from '@lib/sanity'
import type { GeneralSettings } from 'studio/schema'
import { useSnapshot } from 'valtio'
import pageStore from 'data/page'
import _ from 'lodash'
import SEO from '@components/seo'
import { getSeoSettings, getGeneralSettings } from 'data/seo'
import LockdownLayout from '@components/lockdown-layout'

const AboutPage: NextPage<Props> = ({ data, defaultSEO, setting, siteSettings }) => {
  const [loading, setLoading] = useState(true)
  const { pageVisited } = useSnapshot(pageStore)

  useEffect(() => {
    setTimeout(() => {
      setLoading(false)
    }, 1000)
  }, [])

  useEffect(() => {
    if (!_.find(pageVisited, { page: 'about' }) && !loading) {
      pageStore.pageVisited.push({ page: 'about' })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading])

  // Check if lockdown mode is enabled
  if (siteSettings?.lockdown) {
    return (
      <LockdownLayout siteTitle={siteSettings.siteTitle}>
        {/* You can add any additional content here if needed */}
      </LockdownLayout>
    )
  }

  return (
    <CommonLayout theme="dark">
      <SEO
        title={data?.seo?.metaTitle}
        description={data?.seo?.metaDesc}
        image={
          data?.seo?.shareGraphic?.asset?._ref
            ? `https://cdn.sanity.io/images/${
                process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'undefined'
              }/${
                process.env.NEXT_PUBLIC_SANITY_PROJECT_DATASET || 'production'
              }/${data.seo.shareGraphic.asset._ref.split('-')[1]}.${
                data.seo.shareGraphic.asset._ref.split('-')[2] === 'jpeg'
                  ? 'jpg'
                  : data.seo.shareGraphic.asset._ref.split('-')[2]
              }`
            : undefined
        }
        defaultSEO={defaultSEO}
      />
      <AnimatePresence
        initial={false}
        onExitComplete={() => window.scrollTo(0, 0)}
      >
        {!loading && (
          <MotionBox key="work-content">
            <AboutContent page={data} setting={setting} />
          </MotionBox>
        )}
        {loading && !_.find(pageVisited, { page: 'about' }) && (
          <AboutTransition />
        )}
      </AnimatePresence>
    </CommonLayout>
  )
}

const AboutContent: FC<{
  page: PageResult
  setting:
    | (GeneralSettings & {
        _type: 'generalSettings'
      })
    | null
}> = ({ page, setting }) => {
  return (
    <Box
      bg="brand.gray.400"
      width="100%"
      position={['relative', null, null, 'fixed']}
      inset={['auto', null, null, 0]}
    >
      <Grid
        mx={[4, null, null, 8]}
        // my={[0, null, null, 24]}
        pt={[120, null, null, 0]}
        pb={[9, null, null, 0]}
        templateColumns={['1fr', null, null, 'repeat(12, 1fr)']}
        templateRows={[null, null, null, 'repeat(12, 1fr)']}
        // alignContent="center"
        // h="calc(100% - 56px)"
        rowGap={[12, null, null, 0]}
        h={['auto', null, null, '100%']}
      >
        <GridItem
          colSpan={[1, null, null, 5]}
          rowStart={[null, null, null, 4]}
          rowSpan={[null, null, null, 8]}
          textColor="white"
        >
          <Box display={['none', 'block']}>
            <Content blocks={page.description} />
          </Box>
          <Box display={['block', 'none']}>
            <Content blocks={page.descriptionMobile} />
          </Box>
        </GridItem>
        <GridItem
          colStart={[1, null, null, 7]}
          colSpan={[1, null, null, 3]}
          rowStart={[null, null, null, 4]}
          rowSpan={[null, null, null, 8]}
        >
          <Box>
            <Text color="white" as="h3" textStyle="h3" mb={2}>
              Services
            </Text>
            <List listStyleType="none">
              {page.services?.map((service, index) => (
                <ListItem key={index}>
                  <Text textStyle="h2" opacity={0.5}>
                    {service}
                  </Text>
                </ListItem>
              ))}
            </List>
          </Box>
        </GridItem>
        <GridItem
          colStart={[1, null, null, 10]}
          colSpan={[1, null, null, 3]}
          rowStart={[null, null, null, 4]}
          rowSpan={[null, null, null, 8]}
        >
          <Text color="white" as="h3" textStyle="h3" mb={2}>
            Selected Clients
          </Text>
          <List listStyleType="none">
            {page.clients?.map((client, index) => (
              <ListItem key={index}>
                <Text textStyle="h2" opacity={0.5}>
                  {client}
                </Text>
              </ListItem>
            ))}
          </List>
        </GridItem>
        <GridItem
          colStart={[1, null, null, 7]}
          colSpan={[1, null, null, 3]}
          rowStart={[null, null, null, 10]}
          rowSpan={[null, null, null, 8]}
        >
          <VStack align="start" spacing="0">
            <Text color="white" as="h3" textStyle="h3" mb={2}>
              Contact us
            </Text>
            <Link
              variant="underline"
              href={`mailto:${setting?.email}`}
              target="_blank"
              textUnderlineOffset="4px"
            >
              <Text textStyle="h2" opacity={0.5}>
                Email us
              </Text>
            </Link>
            <Link
              variant="underline"
              href={`${setting?.instagram}`}
              target="_blank"
              textUnderlineOffset="4px"
            >
              <Text textStyle="h2" opacity={0.5}>
                Follow us on IG
              </Text>
            </Link>
            <Link
              variant="underline"
              href={`${setting?.vimeo}`}
              target="_blank"
              textUnderlineOffset="4px"
            >
              <Text textStyle="h2" opacity={0.5}>
                Check out our Vimeo
              </Text>
            </Link>
          </VStack>
        </GridItem>
      </Grid>
    </Box>
  )
}

type UnwrapPromise<T> = T extends Promise<infer U> ? U : T
type Props = UnwrapPromise<ReturnType<typeof getStaticProps>>['props']

export async function getStaticProps({
  preview,
  previewData,
}: GetStaticPropsContext<ParsedUrlQuery, { token: string }>) {
  const pageData = await getStaticPage(
    `type == 'about-page' && slug.current == 'about'`
  )

  const settings = await typedSanityClient.getAll('generalSettings')
  const defaultSEO = await getSeoSettings()
  const siteSettings = await getGeneralSettings()

  if (settings?.length <= 0) {
    return {
      props: {
        data: pageData,
        setting: null,
        defaultSEO,
        siteSettings,
      },
      revalidate: 300,
    }
  }

  const setting = await typedSanityClient.get(
    'generalSettings',
    settings[0]._id
  )

  return {
    props: {
      data: pageData,
      setting,
      defaultSEO,
      siteSettings,
    },
    revalidate: 300,
  }
}

export default AboutPage
