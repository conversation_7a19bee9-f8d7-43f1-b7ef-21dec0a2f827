import type {
  SanityReference,
  SanityKeyedReference,
  SanityAs<PERSON>,
  SanityImage,
  SanityFile,
  SanityGeoPoint,
  SanityBlock,
  SanityDocument,
  SanityImageCrop,
  SanityImageHotspot,
  SanityKeyed,
  SanityImageAsset,
  SanityImageMetadata,
  SanityImageDimensions,
  SanityImagePalette,
  SanityImagePaletteSwatch,
} from 'sanity-codegen'

export type {
  SanityReference,
  SanityKeyedReference,
  SanityAsset,
  SanityImage,
  SanityFile,
  SanityGeoPoint,
  SanityBlock,
  SanityDocument,
  SanityImageCrop,
  SanityImageHotspot,
  SanityKeyed,
  SanityImageAsset,
  SanityImageMetadata,
  SanityImageDimensions,
  SanityImagePalette,
  SanityImagePaletteSwatch,
}

/**
 * Page
 *
 *
 */
export interface Page extends SanityDocument {
  _type: 'page'

  /**
   * Type — `string`
   *
   * Page Type
   */
  type?: 'home-page' | 'production-page' | 'about-page' | 'other-page'

  /**
   * Title — `string`
   *
   *
   */
  title?: string

  /**
   * URL Slug — `slug`
   *
   * (required)
   */
  slug?: { _type: 'slug'; current: string }

  /**
   * Description — `complexPortableText`
   *
   *
   */
  description?: ComplexPortableText

  /**
   * Description Mobile — `complexPortableText`
   *
   *
   */
  descriptionMobile?: ComplexPortableText

  /**
   * Contents — `array`
   *
   *
   */
  contents?: Array<
    | SanityKeyed<{
        _type: 'paragraph'
        /**
         * Title — `string`
         *
         *
         */
        title?: string

        /**
         * Content — `complexPortableText`
         *
         *
         */
        content?: ComplexPortableText
      }>
    | SanityKeyed<{
        _type: 'photo'
        /**
         * Title — `string`
         *
         *
         */
        title?: string

        /**
         * Image — `image`
         *
         *
         */
        image?: {
          _type: 'image'
          asset: SanityReference<SanityImageAsset>
          crop?: SanityImageCrop
          hotspot?: SanityImageHotspot

          /**
           * Display Size (aspect ratio) — `number`
           *
           *
           */
          customRatio?: number

          /**
           * Alternative text — `string`
           *
           * Important for SEO and accessiblity.
           */
          alt?: string
        }
      }>
  >

  /**
   * Landing Image — `object`
   *
   *
   */
  landingImage?: {
    _type: 'landingImage'
    /**
     * Title — `complexPortableText`
     *
     *
     */
    title?: ComplexPortableText

    /**
     * Image — `image`
     *
     *
     */
    image?: {
      _type: 'image'
      asset: SanityReference<SanityImageAsset>
      crop?: SanityImageCrop
      hotspot?: SanityImageHotspot

      /**
       * Display Size (aspect ratio) — `number`
       *
       *
       */
      customRatio?: number

      /**
       * Alternative text — `string`
       *
       * Important for SEO and accessiblity.
       */
      alt?: string
    }
  }

  /**
   * Landing Work — `object`
   *
   *
   */
  landingWork?: {
    _type: 'landingWork'
    /**
     * Gallery — `reference`
     *
     *
     */
    gallery?: SanityReference<Work>
  }

  /**
   * Contents — `array`
   *
   *
   */
  homeContents?: Array<SanityKeyed<HomeLayout>>

  /**
   * Contents — `array`
   *
   *
   */
  productionContents?: Array<
    SanityKeyed<{
      _type: 'productionContent'
      /**
       * Title — `string`
       *
       *
       */
      title?: string

      /**
       * Content — `complexPortableText`
       *
       *
       */
      content?: ComplexPortableText

      /**
       * Image — `image`
       *
       *
       */
      image?: {
        _type: 'image'
        asset: SanityReference<SanityImageAsset>
        crop?: SanityImageCrop
        hotspot?: SanityImageHotspot

        /**
         * Display Size (aspect ratio) — `number`
         *
         *
         */
        customRatio?: number

        /**
         * Alternative text — `string`
         *
         * Important for SEO and accessiblity.
         */
        alt?: string
      }
    }>
  >

  /**
   * Services — `array`
   *
   *
   */
  services?: Array<SanityKeyed<string>>

  /**
   * Clients — `array`
   *
   *
   */
  clients?: Array<SanityKeyed<string>>

  /**
   * SEO / Share Settings — `seo`
   *
   *
   */
  seo?: Seo
}

/**
 * Work
 *
 *
 */
export interface Work extends SanityDocument {
  _type: 'work'

  /**
   * Title — `string`
   *
   *
   */
  title?: string

  /**
   * URL Slug — `slug`
   *
   * (required)
   */
  slug?: { _type: 'slug'; current: string }

  /**
   * Categories — `array`
   *
   *
   */
  categories?: Array<SanityKeyedReference<WorkCategory>>

  /**
   * Month & Year — `date`
   *
   *
   */
  monthYear?: string

  /**
   * Type — `string`
   *
   *
   */
  workType?: string

  /**
   * Theme Color — `color`
   *
   *
   */
  themeColor?: Color

  /**
   * Gallery — `array`
   *
   *
   */
  gallery?: Array<
    | SanityKeyed<{
        _type: 'photo'
        asset: SanityReference<SanityImageAsset>
        crop?: SanityImageCrop
        hotspot?: SanityImageHotspot

        /**
         * Display Size (aspect ratio) — `number`
         *
         *
         */
        customRatio?: number

        /**
         * Alternative text — `string`
         *
         * Important for SEO and accessiblity.
         */
        alt?: string
      }>
    | SanityKeyed<{
        _type: 'video'
        asset: SanityReference<any>
      }>
    | SanityKeyed<{
        _type: 'vimeo'
        /**
         * Vimeo ID — `string`
         *
         * ID from Vimeo Video
         */
        vimeo_id?: string
        /**
         * Thumbnail — `image`
         *
         *
         */
        thumbnail?: {
          _type: 'image'
          asset: SanityReference<SanityImageAsset>
          crop?: SanityImageCrop
          hotspot?: SanityImageHotspot

          /**
           * Display Size (aspect ratio) — `number`
           *
           *
           */
          customRatio?: number

          /**
           * Alternative text — `string`
           *
           * Important for SEO and accessiblity.
           */
          alt?: string
        }
      }>
  >

  /**
   * Featured Image — `image`
   *
   *
   */
  featuredImage?: {
    _type: 'image'
    asset: SanityReference<SanityImageAsset>
    crop?: SanityImageCrop
    hotspot?: SanityImageHotspot

    /**
     * Display Size (aspect ratio) — `number`
     *
     *
     */
    customRatio?: number

    /**
     * Alternative text — `string`
     *
     * Important for SEO and accessiblity.
     */
    alt?: string
  }

  /**
   * Hover Image — `array`
   *
   *
   */
  hoverImages?: Array<
    SanityKeyed<{
      _type: 'photo'
      asset: SanityReference<SanityImageAsset>
      crop?: SanityImageCrop
      hotspot?: SanityImageHotspot

      /**
       * Display Size (aspect ratio) — `number`
       *
       *
       */
      customRatio?: number

      /**
       * Alternative text — `string`
       *
       * Important for SEO and accessiblity.
       */
      alt?: string
    }>
  >

  /**
   * SEO / Share Settings — `seo`
   *
   *
   */
  seo?: Seo
}

/**
 * Category
 *
 *
 */
export interface WorkCategory extends SanityDocument {
  _type: 'workCategory'

  /**
   * Name — `string`
   *
   *
   */
  name: string

  /**
   * Value — `string`
   *
   * (required)
   */
  value: string
}

/**
 * General Settings
 *
 *
 */
export interface GeneralSettings extends SanityDocument {
  _type: 'generalSettings'

  /**
   * Home Page — `reference`
   *
   * This page will show at the root of your domain
   */
  home?: SanityReference<Page>

  /**
   * Error Page (404) — `reference`
   *
   * This page will show for any URL at your domain that does not exist yet
   */
  error?: SanityReference<Page>

  /**
   * Site Title — `string`
   *
   * The name of your site, usually your company/brand name
   */
  siteTitle?: string

  /**
   * Live Site URL — `url`
   *
   * The root domain or subdomain of your website
   */
  siteURL?: string

  /**
   * Google Tag Manager (GTM) — `string`
   *
   * To enable GTM enter your Container ID
   */
  gtmID?: string

  /**
   * Klaviyo Site ID (Public API Key) — `string`
   *
   * For product waitlists and newsletter forms
   */
  klaviyoAccountID?: string

  /**
   * Email — `string`
   *
   * Email to contact
   */
  email?: string

  /**
   * Instagram — `string`
   *
   * Link of Instagram
   */
  instagram?: string

  /**
   * Vimeo — `string`
   *
   * Link to Vimeo
   */
  vimeo?: string
}

/**
 * Cookie Consent Settings
 *
 *
 */
export interface CookieSettings extends SanityDocument {
  _type: 'cookieSettings'

  /**
   * Enable Cookie Consent? — `boolean`
   *
   *
   */
  enabled?: boolean

  /**
   * Message — `text`
   *
   * Your cookie consent message
   */
  message?: string

  /**
   * Link — `reference`
   *
   * Show a link to "Learn More" about your cookie policy
   */
  link?: SanityReference<Page>
}

/**
 * Header Settings
 *
 *
 */
export interface HeaderSettings extends SanityDocument {
  _type: 'headerSettings'

  /**
   * Desktop Menu (Left) — `reference`
   *
   *
   */
  menuDesktopLeft?: SanityReference<Menu>

  /**
   * Desktop Menu (Right) — `reference`
   *
   *
   */
  menuDesktopRight?: SanityReference<Menu>

  /**
   * Mobile Menu (Primary) — `reference`
   *
   *
   */
  menuMobilePrimary?: SanityReference<Menu>

  /**
   * Mobile Menu (Secondary) — `reference`
   *
   *
   */
  menuMobileSecondary?: SanityReference<Menu>
}

/**
 * Default SEO / Share
 *
 *
 */
export interface SeoSettings extends SanityDocument {
  _type: 'seoSettings'

  /**
   * Default Meta Title — `string`
   *
   * Title used for search engines and browsers
   */
  metaTitle?: string

  /**
   * Default Meta Description — `text`
   *
   * Description for search engines
   */
  metaDesc?: string

  /**
   * Default Share Title — `string`
   *
   * Title used for social sharing cards
   */
  shareTitle?: string

  /**
   * Default Share Description — `text`
   *
   * Description used for social sharing cards
   */
  shareDesc?: string

  /**
   * Default Share Graphic — `image`
   *
   * Recommended size: 1200x630 (PNG or JPG)
   */
  shareGraphic?: {
    _type: 'image'
    asset: SanityReference<SanityImageAsset>
    crop?: SanityImageCrop
    hotspot?: SanityImageHotspot
  }

  /**
   * Browser Icon (Favicon) — `image`
   *
   * Upload a 16 x 16 SVG icon to use as the browser icon
   */
  favicon?: {
    _type: 'image'
    asset: SanityReference<SanityImageAsset>
    crop?: SanityImageCrop
    hotspot?: SanityImageHotspot
  }

  /**
   * Legacy Browser Icon (.ico) — `file`
   *
   * Upload a 32 x 32 .ico file for older browsers
   */
  faviconLegacy?: { _type: 'file'; asset: SanityReference<any> }

  /**
   * Touch Icon — `image`
   *
   * Recommended size: 192x192 (PNG)
   */
  touchIcon?: {
    _type: 'image'
    asset: SanityReference<SanityImageAsset>
    crop?: SanityImageCrop
    hotspot?: SanityImageHotspot
  }
}

/**
 * Menu
 *
 *
 */
export interface Menu extends SanityDocument {
  _type: 'menu'

  /**
   * Title — `string`
   *
   *
   */
  title?: string

  /**
   * Slug — `slug`
   *
   * required
   */
  slug?: { _type: 'slug'; current: string }

  /**
   * Nav Items — `array`
   *
   *
   */
  items?: Array<SanityKeyed<string>>
}

export type ComplexPortableText = Array<
  | SanityKeyed<SanityBlock>
  | SanityKeyed<{
      _type: 'break'
      /**
       * break — `string`
       *
       *
       */
      break?: string
    }>
>

export type HorizontalRule = {
  _type: 'horizontalRule'
  /**
   * horizontalRule — `string`
   *
   *
   */
  horizontalRule?: string
}

export type Seo = {
  _type: 'seo'
  /**
   * Meta Title — `string`
   *
   * Title used for search engines and browsers
   */
  metaTitle?: string

  /**
   * Meta Description — `text`
   *
   * Description for search engines
   */
  metaDesc?: string

  /**
   * Share Title — `string`
   *
   * Title used for social sharing cards
   */
  shareTitle?: string

  /**
   * Share Description — `text`
   *
   * Description used for social sharing cards
   */
  shareDesc?: string

  /**
   * Share Graphic — `image`
   *
   * Recommended size: 1200x630 (PNG or JPG)
   */
  shareGraphic?: {
    _type: 'image'
    asset: SanityReference<SanityImageAsset>
    crop?: SanityImageCrop
    hotspot?: SanityImageHotspot
  }
}

export type HomeLayout = {
  _type: 'homeLayout'
  /**
   * Type — `string`
   *
   * Layout Type
   */
  type?: 'a' | 'b' | 'c' | 'd'

  /**
   * Gallery — `array`
   *
   *
   */
  gallery?: Array<SanityKeyedReference<Work>>
}

export type Documents =
  | Page
  | Work
  | WorkCategory
  | GeneralSettings
  | CookieSettings
  | HeaderSettings
  | SeoSettings
  | Menu

/**
 * This interface is a stub. It was referenced in your sanity schema but
 * the definition was not actually found. Future versions of
 * sanity-codegen will let you type this explicity.
 */
type Color = any
