{"name": "oooze-sanity", "private": true, "version": "1.0.0", "description": "", "main": "package.json", "author": "gaincue", "license": "UNLICENSED", "scripts": {"start": "sanity start", "build": "sanity build", "generate": "npx sanity-codegen", "deploy": "sanity deploy"}, "keywords": ["sanity"], "dependencies": {"@sanity/base": "^2.29.8", "@sanity/color-input": "^2.29.8", "@sanity/core": "^2.29.5", "@sanity/default-layout": "^2.29.8", "@sanity/default-login": "^2.29.8", "@sanity/desk-tool": "^2.29.8", "@sanity/eslint-config-studio": "^2.0.0", "@sanity/vision": "^2.29.8", "classnames": "^2.3.1", "eslint": "^8.6.0", "phosphor-react": "^1.4.1", "prop-types": "^15.7", "react": "^17.0", "react-dom": "^17.0", "sanity-plugin-color-picker": "^1.0.3", "sanity-plugin-media": "^1.4.4", "styled-components": "^5.2.0"}, "devDependencies": {"prettier": "^2.6.2", "sanity-codegen": "^0.9.8"}}