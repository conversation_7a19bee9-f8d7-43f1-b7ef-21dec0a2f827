import React from 'react'
import cx from 'classnames'
import _ from 'lodash'

export const Header1 = (props) => (
  <h1
    style={{
      margin: 0,
      fontSize: '32px',
      fontWeight: 400,
      textAlign: setTextAlign(props),
    }}
  >
    {props.children}
  </h1>
)

export const Header2 = (props) => (
  <h2
    style={{
      margin: 0,
      fontSize: '24px',
      fontWeight: 400,
      textAlign: setTextAlign(props),
    }}
  >
    {props.children}
  </h2>
)

export const Header3 = (props) => (
  <h3
    style={{
      margin: 0,
      fontSize: '12px',
      fontWeight: 400,
      textAlign: setTextAlign(props),
    }}
  >
    {props.children}
  </h3>
)

export const Body = (props) => (
  <p
    style={{
      margin: 0,
      fontSize: '13px',
      letterSpacing: '0.02em',
      textAlign: setTextAlign(props),
    }}
  >
    {props.children}
  </p>
)

export const Italic = (props) => (
  <p
    style={{
      margin: 0,
      fontStyle: 'italic',
      textAlign: setTextAlign(props),
    }}
  >
    {props.children}
  </p>
)

export const Button = ({ isButton, styles, children }) => {
  if (!isButton) return children

  return (
    <span
      className={cx('btn', styles?.style, {
        'is-large': styles?.isLarge,
        'is-block': styles?.isBlock,
      })}
    >
      {children}
    </span>
  )
}

export const Color = ({ colorSelect, children }) => {
  return <span style={{ color: colorSelect }}>{children}</span>
}

export const TextAlign = ({ alignment, children }) => {
  return (
    <div style={{ textAlign: alignment }}>
      <span>{children}</span>
    </div>
  )
}

export const CenterAlign = ({ children }) => {
  return <p style={{ textAlign: 'center' }}>{children}</p>
}

export const Break = () => <br />

export const TextStyle = ({
  isBold,
  isItalic,
  isUnderline,
  fontSize,
  colorSelect,
  children,
}) => {
  return (
    <span
      style={{
        fontWeight: isBold && 'bold',
        fontStyle: isItalic && 'italic',
        textDecoration: isUnderline && 'underline',
        fontSize: fontSize,
        color: colorSelect?.hex,
      }}
    >
      {children}
    </span>
  )
}

const setTextAlign = (props) => {
  return _.first(_.first(props.children)?.props?.parent?.markDefs)?.styles
    ?.textAlign
}
