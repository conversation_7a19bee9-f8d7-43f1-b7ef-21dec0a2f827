// First, we must import the schema creator
import createSchema from 'part:@sanity/base/schema-creator'
// Then import schema types from any plugins that might expose them
import schemaTypes from 'all:part:@sanity/base/schema-type'

import page from './documents/page'
import work from './documents/work'

import generalSettings from './documents/settings-general'
import cookieSettings from './documents/settings-cookie'
import headerSettings from './documents/settings-header'
// import footerSettings from './documents/settings-footer'
import seoSettings from './documents/settings-seo'
import menu from './documents/menu'
import workCategory from './documents/work-category'

import horizontalRule from './objects/horizontal-rule'
import complexPortableText from './objects/portable-complex'
import seo from './objects/seo'
import homeLayout from './objects/home-layout'

// Then we give our schema to the builder and provide the result to Sanity
export default createSchema({
  name: 'default',
  types: schemaTypes.concat([
    page,
    work,
    workCategory,

    generalSettings,
    cookieSettings,
    headerSettings,
    // footerSettings,
    seoSettings,
    menu,

    // objects
    complexPortableText,
    horizontalRule,
    seo,
    homeLayout,
  ]),
})
