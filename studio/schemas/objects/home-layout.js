import { Layout } from 'phosphor-react'
import customImage from '../../lib/custom-image'

export default {
  title: 'Layout',
  name: 'homeLayout',
  type: 'object',
  icon: Layout,
  fields: [
    {
      title: 'Type',
      name: 'type',
      type: 'string',
      description: 'Layout Type',
      options: {
        list: [
          { title: 'A (two big images)', value: 'a' },
          { title: 'B (one large image)', value: 'b' },
          { title: 'C (two small & one big image)', value: 'c' },
          { title: 'D (one big & two small image)', value: 'd' },
        ],
      },
    },
    {
      title: 'Gallery',
      name: 'gallery',
      type: 'array',
      of: [
        {
          title: 'Work',
          name: 'work',
          type: 'reference',
          to: [{ type: 'work' }],
        },
      ],
      validation: (Rule) => {
        return Rule.custom((field, { parent }) => {
          if (parent.type == 'a') {
            if (field.length > 2) {
              return 'Layout A only accept two images'
            }
          }

          if (parent.type == 'b') {
            if (field.length > 2) {
              return 'Layout B only accept one image'
            }
          }

          if (parent.type == 'c') {
            if (field.length > 3) {
              return 'Layout C only accept three images'
            }
          }

          if (parent.type == 'd') {
            if (field.length > 3) {
              return 'Layout D only accept three images'
            }
          }

          return true
        })
      },
    },
  ],
  preview: {
    select: {
      type: 'type',
      gallery: 'gallery',
    },
    prepare({ type, gallery }) {
      return {
        title: `Layout ${type.toUpperCase()}`,
        media: gallery[0],
      }
    },
  },
}
