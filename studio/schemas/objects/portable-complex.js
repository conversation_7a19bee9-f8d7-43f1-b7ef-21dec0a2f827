import { TextAlignCenter } from 'phosphor-react'
import {
  Body,
  Header1,
  Header2,
  Header3,
  Break,
} from '../../components/block-renders'
// import customImage from '../../lib/custom-image'

export default {
  title: 'Rich Text',
  name: 'complexPortableText',
  type: 'array',
  of: [
    {
      title: 'Block',
      type: 'block',
      styles: [
        {
          title: 'Paragraph',
          value: 'body',
          blockEditor: {
            render: Body,
          },
        },
        {
          title: 'Subtitle',
          value: 'subtitle',
          blockEditor: {
            render: Body,
          },
        },
        {
          title: 'H1 (use once)',
          value: 'h1',
          blockEditor: {
            render: Header1,
          },
        },
        {
          title: 'H2',
          value: 'h2',
          blockEditor: {
            render: Header2,
          },
        },
        {
          title: 'H3',
          value: 'h3',
          blockEditor: {
            render: Header3,
          },
        },
        // {
        //   title: 'Italic',
        //   value: 'italic',
        //   blockEditor: {
        //     render: Italic,
        //   },
        // },
      ],
      lists: [{ title: 'Bullet', value: 'bullet' }],
      marks: {
        annotations: [
          {
            title: 'Link',
            name: 'link',
            type: 'object',
            // blockEditor: {
            //   render: Button,
            // },
            fields: [
              {
                title: 'Link Type',
                name: 'linkType',
                type: 'string',
                options: {
                  list: [
                    { title: 'Internal Page', value: 'internal' },
                    { title: 'External URL', value: 'external' },
                  ],
                },
                initialValue: 'internal',
                validation: (Rule) => Rule.required(),
              },
              {
                title: 'Internal Page',
                name: 'page',
                type: 'reference',
                to: [{ type: 'page' }],
                hidden: ({ parent }) => parent.linkType !== 'internal',
              },
              {
                title: 'External URL',
                name: 'url',
                type: 'url',
                hidden: ({ parent }) => parent.linkType !== 'external',
              },
              // {
              //   title: 'Style as Button?',
              //   name: 'isButton',
              //   type: 'boolean',
              //   initialValue: false,
              // },
              // {
              //   name: 'styles',
              //   type: 'object',
              //   fields: [
              //     {
              //       title: 'Button Style',
              //       name: 'style',
              //       type: 'string',
              //       options: {
              //         list: [
              //           { title: 'Default', value: '' },
              //           { title: 'Primary', value: 'is-primary' },
              //           { title: 'White', value: 'is-white' },
              //         ],
              //         layout: 'radio',
              //       },
              //     },
              //     {
              //       title: 'Large Size',
              //       name: 'isLarge',
              //       type: 'boolean',
              //       options: {
              //         layout: 'checkbox',
              //       },
              //       initialValue: false,
              //     },
              //     {
              //       title: 'Full Width',
              //       name: 'isBlock',
              //       type: 'boolean',
              //       options: {
              //         layout: 'checkbox',
              //       },
              //       initialValue: false,
              //     },
              //   ],
              //   hidden: ({ parent }) => !parent.isButton,
              // },
            ],
          },
          // {
          //   title: 'Style',
          //   name: 'style',
          //   type: 'object',
          //   blockEditor: {
          //     icon: () => 'S',
          //     render: TextStyle,
          //   },
          //   fields: [
          //     {
          //       title: 'Bold',
          //       name: 'isBold',
          //       type: 'boolean',
          //       initialValue: false,
          //     },
          //     {
          //       title: 'Italic',
          //       name: 'isItalic',
          //       type: 'boolean',
          //       initialValue: false,
          //     },
          //     {
          //       title: 'Underline',
          //       name: 'isUnderline',
          //       type: 'boolean',
          //       initialValue: false,
          //     },
          //     {
          //       title: 'Font Size',
          //       name: 'fontSize',
          //       type: 'number',
          //       options: {
          //         list: [
          //           6, 7, 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30,
          //           36, 42, 48, 60, 72, 96,
          //         ],
          //       },
          //       initialValue: 16,
          //     },
          //     {
          //       title: 'Color Select',
          //       name: 'colorSelect',
          //       type: 'color',
          //     },
          //   ],
          // },
          {
            title: 'Alignment',
            name: 'alignment',
            type: 'object',
            icon: TextAlignCenter,
            fields: [
              {
                name: 'styles',
                type: 'object',
                fields: [
                  {
                    title: 'Text Align',
                    name: 'textAlign',
                    type: 'string',
                    options: {
                      list: [
                        { title: 'Left', value: 'left' },
                        { title: 'Center', value: 'center' },
                        { title: 'Right', value: 'right' },
                      ],
                      layout: 'radio',
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    },
    {
      title: 'Break',
      name: 'break',
      type: 'object',
      fields: [
        {
          type: 'string',
          name: 'break',
          inputComponent: Break,
        },
      ],
      preview: {
        prepare() {
          return {
            title: 'Break',
          }
        },
      },
    },
    // customImage(),
    // {
    //   type: 'horizontalRule',
    // },
  ],
}
