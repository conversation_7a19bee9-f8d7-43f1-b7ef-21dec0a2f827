import { Briefcase } from 'phosphor-react'

import customImage from '../../lib/custom-image'

export default {
  name: 'work',
  title: 'Work',
  type: 'document',
  icon: Briefcase,
  fieldsets: [
    {
      title: 'Project Grid',
      name: 'grid',
      description: 'Define how this project should appear on grid',
      options: { columns: 2 },
    },
  ],
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
    },
    {
      title: 'URL Slug',
      name: 'slug',
      type: 'slug',
      description: '(required)',
      options: {
        source: 'title',
        maxLength: 96,
      },
    },
    {
      title: 'Categories',
      name: 'categories',
      type: 'array',
      of: [{ type: 'reference', to: [{ type: 'workCategory' }] }],
    },
    {
      title: 'Month & Year',
      name: 'monthYear',
      type: 'date',
    },
    {
      title: 'Type',
      name: 'workType',
      type: 'string',
    },
    {
      title: 'Theme Color',
      name: 'themeColor',
      type: 'color',
    },
    // {
    //   title: 'Project Content',
    //   name: 'projectContent',
    //   type: 'array',
    //   of: [{ type: 'projectRow' }],
    // },
    {
      title: 'Gallery',
      name: 'gallery',
      type: 'array',
      of: [
        customImage(),
        {
          title: 'Video',
          name: 'video',
          type: 'file',
          options: {
            accept: 'video/*',
          },
        },
        {
          title: 'Vimeo Video',
          name: 'vimeo',
          type: 'object',
          fields: [
            {
              title: 'Vimeo ID',
              name: 'vimeo_id',
              type: 'string',
              description: 'ID from Vimeo Video',
            },
            customImage({
              title: 'Thumbnail',
              name: 'thumbnail',
              // fieldset: 'grid',
            }),
          ],
          preview: {
            select: {
              title: 'vimeo_id',
            },
            prepare({ title }) {
              return {
                title: 'Vimeo ' + title,
              }
            },
          },
        },
      ],
    },
    customImage({
      title: 'Featured Image',
      name: 'featuredImage',
      fieldset: 'grid',
    }),
    {
      title: 'Hover Image',
      name: 'hoverImages',
      type: 'array',
      of: [customImage()],
      fieldset: 'grid',
      validation: (Rule) =>
        Rule.max(2).warning('Only 2 hover images are allowed'),
    },
    {
      title: 'SEO / Share Settings',
      name: 'seo',
      type: 'seo',
    },
  ],
  preview: {
    select: {
      title: 'title',
      slug: 'slug',
    },
    prepare({ title = 'Untitled' }) {
      return {
        title,
      }
    },
  },
  orderings: [
    {
      title: 'Project Date, New',
      name: 'monthYearDesc',
      by: [{ field: 'monthYear', direction: 'desc' }],
    },
    {
      title: 'Project Date, Old',
      name: 'monthYearAsc',
      by: [{ field: 'monthYear', direction: 'asc' }],
    },
  ],
}
