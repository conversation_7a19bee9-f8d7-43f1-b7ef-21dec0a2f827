import React from 'react'

export default {
  title: 'General Settings',
  name: 'generalSettings',
  type: 'document',
  groups: [
    { title: 'Site Details', name: 'details', default: true },
    { title: 'Displays', name: 'displays' },
    { title: 'Advanced', name: 'advanced' },
    { title: 'Contacts', name: 'contact' },
  ],
  fields: [
    {
      title: 'Home Page',
      name: 'home',
      type: 'reference',
      to: [{ type: 'page' }],
      description: 'This page will show at the root of your domain',
      group: 'displays',
      options: {
        filter: 'type == $type',
        filterParams: { type: 'home-page' },
      },
    },
    {
      title: 'Error Page (404)',
      name: 'error',
      type: 'reference',
      to: [{ type: 'page' }],
      description:
        'This page will show for any URL at your domain that does not exist yet',
      group: 'displays',
    },
    {
      title: 'Site Title',
      name: 'siteTitle',
      type: 'string',
      description: 'The name of your site, usually your company/brand name',
      group: 'details',
    },
    {
      title: 'Live Site URL',
      description: 'The root domain or subdomain of your website',
      name: 'siteURL',
      type: 'url',
      validation: (Rule) => Rule.required(),
      group: 'details',
    },
    {
      title: 'Lockdown',
      description: 'Lockdown mode',
      name: 'lockdown',
      type: 'boolean',
      group: 'details',
    },
    // {
    //   title: 'Google Tag Manager (GTM)',
    //   description: 'To enable GTM enter your Container ID',
    //   name: 'gtmID',
    //   type: 'string',
    //   group: 'advanced',
    // },
    {
      title: 'Email',
      description: 'Email to contact',
      name: 'email',
      type: 'string',
      group: 'contact',
    },
    {
      title: 'Instagram',
      description: 'Link of Instagram',
      name: 'instagram',
      type: 'string',
      group: 'contact',
    },
    {
      title: 'Vimeo',
      description: 'Link to Vimeo',
      name: 'vimeo',
      type: 'string',
      group: 'contact',
    },
  ],
  preview: {
    prepare() {
      return {
        title: 'General Settings',
      }
    },
  },
}
