import { PaperPlaneTilt, List } from 'phosphor-react'

export default {
  title: 'Footer Settings',
  name: 'footerSettings',
  type: 'document',
  groups: [
    {
      title: 'Block 1',
      name: 'column1',
      icon: PaperPlaneTilt,
      default: true,
    },
    {
      title: 'Block 2',
      name: 'column2',
      icon: List,
    },
  ],
  fields: [
    {
      title: 'Contacts',
      name: 'contacts',
      type: 'complexPortableText',
      group: 'column1',
    },
    {
      title: 'Menu',
      name: 'blockMenu',
      type: 'reference',
      to: [{ type: 'menu' }],
      group: 'column2',
    },
  ],
  preview: {
    prepare() {
      return {
        title: 'Footer Settings',
      }
    },
  },
}
