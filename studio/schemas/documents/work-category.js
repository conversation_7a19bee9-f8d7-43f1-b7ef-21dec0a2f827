import { SortAscending } from 'phosphor-react'

export default {
  title: 'Category',
  name: 'workCategory',
  type: 'document',
  icon: SortAscending,
  fields: [
    {
      title: 'Name',
      name: 'name',
      type: 'string',
      codegen: { required: true },
      validation: (Rule) => Rule.required(),
    },
    {
      title: 'Value',
      name: 'value',
      type: 'string',
      description: '(required)',
      options: {
        source: 'name',
        maxLength: 96,
      },
      codegen: { required: true },
      validation: (Rule) => Rule.required(),
    },
  ],
  preview: {
    select: {
      title: 'name',
      value: 'value',
    },
    prepare({ title = 'Untitled', value }) {
      return {
        title,
        subtitle: value ?? '(missing value)',
      }
    },
  },
}
