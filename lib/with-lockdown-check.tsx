import React from 'react'
import LockdownLayout from '@components/lockdown-layout'
import { GeneralSiteSettings } from 'data/seo'

interface WithLockdownProps {
  siteSettings?: GeneralSiteSettings
  [key: string]: any
}

export function withLockdownCheck<P extends WithLockdownProps>(
  WrappedComponent: React.ComponentType<P>
) {
  const WithLockdownComponent = (props: P) => {
    // Check if lockdown mode is enabled
    if (props.siteSettings?.lockdown) {
      return (
        <LockdownLayout siteTitle={props.siteSettings.siteTitle}>
          {/* You can add any additional content here if needed */}
        </LockdownLayout>
      )
    }

    // If not in lockdown mode, render the normal component
    return <WrappedComponent {...props} />
  }

  WithLockdownComponent.displayName = `withLockdownCheck(${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`

  return WithLockdownComponent
}

export default withLockdownCheck
