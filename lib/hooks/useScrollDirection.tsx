import { useState, useEffect } from 'react'

const useScrollDirection = (threshold = 100) => {
  const [scrollDirection, setScrollDirection] = useState('up')
  const [lastScrollPosition, setLastScrollPosition] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollPosition = window.pageYOffset

      if (currentScrollPosition > lastScrollPosition) {
        setScrollDirection('down')
      } else if (currentScrollPosition < lastScrollPosition - threshold) {
        setScrollDirection('up')
      }

      setLastScrollPosition(currentScrollPosition)
    }

    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [lastScrollPosition, threshold])

  return scrollDirection
}

export default useScrollDirection
