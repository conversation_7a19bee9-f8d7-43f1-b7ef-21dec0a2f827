import {
  ChakraProvider,
  cookieStorageManager,
  cookieStorageManagerSSR,
  localStorageManager,
} from '@chakra-ui/react'
import { GetServerSidePropsContext } from 'next'
import { ReactNode } from 'react'
import Fonts from 'style/fonts'
import theme from 'style/theme'

interface ChakraProps {
  cookies?: string
  children: ReactNode
  overrideMode?: string
}

export const Chakra = ({ children, cookies, overrideMode }: ChakraProps) => {
  return (
    <ChakraProvider
      theme={theme}
      colorModeManager={
        overrideMode
          ? cookieStorageManagerSSR(`chakra-ui-color-mode=${overrideMode}`)
          : cookies
          ? cookieStorageManagerSSR(cookies)
          : localStorageManager
      }
    >
      <Fonts />
      {children}
    </ChakraProvider>
  )
}

export type ServerSideProps<T> = { props: T } | Promise<{ props: T }>

export function getServerSideProps({
  req,
}: GetServerSidePropsContext): ServerSideProps<{ cookies?: string }> {
  return {
    props: {
      cookies: req.headers.cookie ?? '',
    },
  }
}
