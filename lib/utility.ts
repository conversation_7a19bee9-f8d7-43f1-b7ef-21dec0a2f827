const protobuf = require('protobufjs')

const buf = `package image;
  syntax = "proto3";
  
  message Share {
    string i = 1;
    string p = 2;
  }
  
  message share_request {
    repeated Share s = 1;
  }

  message Image {
    repeated bytes b = 1;
  }
`

interface Payload {
  s: {
    i: string
    p: string
  }[]
}

export async function encodeShare(payload: Payload): Promise<Uint8Array> {
  var root = protobuf.parse(buf).root
  const ShareRequest = root.lookupType('image.share_request')
  const message = ShareRequest.create(payload)
  return ShareRequest.encode(message).finish()
}

export async function decodeShare(buffer: Uint8Array): Promise<Payload> {
  var root = protobuf.parse(buf).root
  const ShareRequest = root.lookupType('image.share_request')
  const err = ShareRequest.verify(buffer)
  if (err) {
    throw err
  }
  const message = ShareRequest.decode(buffer)
  return ShareRequest.toObject(message)
}

// interface ImagePayload {
//   b: Buffer[]
// }

// export async function encodeImage(payload: ImagePayload): Promise<Uint8Array> {
//   var root = protobuf.parse(buf).root
//   const Image = root.lookupType('image.Image')
//   const message = Image.create(payload)
//   return Image.encode(message).finish()
// }

// export async function decodeImage(buffer: Buffer): Promise<ImagePayload> {
//   var root = protobuf.parse(buf).root
//   const Image = root.lookupType('image.Image')
//   const err = Image.verify(buffer)
//   if (err) {
//     throw err
//   }
//   const message = Image.decode(buffer)
//   return Image.toObject(message)
// }

export function clamp(number: number, min: number, max: number) {
  return Math.max(min, Math.min(number, max));
}
