import { createClient } from 'sanity-codegen'
import createSanityClient, { ClientConfig } from '@sanity/client'
import sanityImage from '@sanity/image-url'
import fetch from 'node-fetch'

import { Documents } from '../studio/schema'

interface SanityConfig {
  active: boolean
  token: string
  typed: boolean
}

// copied from sanity-codegen
interface CreateClientOptions {
  projectId: string
  dataset: string
  fetch: any // WindowOrWorkerGlobalScope['fetch']
  token?: string
  previewMode?: boolean
  useCdn?: boolean
  apiVersion?: string
}

let dataset = process.env.SANITY_PROJECT_DATASET || ''
let projectId = process.env.SANITY_PROJECT_ID || ''

const options: CreateClientOptions = {
  dataset,
  projectId,
  fetch: typeof window !== 'undefined' ? window.fetch : fetch,
  useCdn: process.env.NODE_ENV === 'production',
  apiVersion: '2021-03-25',
}
// use typed client if we don't have references
export const typedSanityClient = createClient<Documents>(options)
// use raw client if we have references
export const rawSanityClient = createSanityClient(options)
export const imageBuilder = sanityImage(rawSanityClient)

export function createPreviewClient(token: string) {
  return createClient<Documents>({
    ...options,
    useCdn: false,
    token,
  })
}

export function getSanityClient(
  config: SanityConfig = { active: false, token: '', typed: true }
) {
  if (config?.active) {
    return createPreviewClient(config.token)
  } else if (config?.typed) {
    return typedSanityClient
  } else {
    return rawSanityClient
  }
}
