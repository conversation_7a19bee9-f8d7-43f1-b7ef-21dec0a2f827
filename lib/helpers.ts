import {
  ImageFormat,
  SanityImageSource,
} from '@sanity/image-url/lib/types/types'
import { ImageUrlBuilder } from 'next-sanity-image'
import { imageBuilder } from './sanity'

interface ImageArgument {
  width?: string | number
  height?: string | number
  format?: ImageFormat
  quality?: number
  srcSizes?: string[] | number[]
  aspect?: number
}

export function buildSrc(
  image: SanityImageSource,
  { width, height, format, quality }: ImageArgument
) {
  let imgSrc = imageBuilder.image(image)

  if (width) {
    imgSrc = imgSrc.width(Math.round(width as number))
  }

  if (height) {
    imgSrc = imgSrc.height(Math.round(height as number))
  }

  if (format) {
    imgSrc = imgSrc.format(format)
  }

  if (quality) {
    imgSrc = imgSrc.quality(quality)
  }

  return imgSrc.fit('max').auto('format').url()
}

export function buildSrcSet(
  image: SanityImageSource,
  { srcSizes, aspect, format, quality }: ImageArgument
) {
  if (!srcSizes) {
    return
  }

  const sizes = srcSizes.map((width) => {
    let imgSrc: any = buildSrc(image, {
      ...{ width },
      height: aspect && Math.round((width as number) * aspect) / 100,
      ...{ format },
      ...{ quality },
    })

    // TODO
    if (format) {
      imgSrc = (imgSrc as ImageUrlBuilder).format(format).url()
    }

    return `${imgSrc} ${width}w`
  })

  return sizes.join(',')
}
