import { extendTheme } from '@chakra-ui/react'

// breakpoints
// [0, 479px, 767px, 991px, 1279px, 1535px]

const customTheme = {
  initialColorMode: 'light',
  useSystemColorMode: false,
  components: {
    Link: {
      baseStyle: {
        _focus: {
          boxShadow: 'none',
        },
        fontFamily: 'body',
        fontSize: '13px',
        letterSpacing: '0.02em',
        textUnderlineOffset: '8px',
        _hover: {
          textDecorationThickness: '2px',
        },
      },
      variants: {
        underline: {
          textDecoration: 'underline',
        },
      },
    },
    Button: {
      variants: {
        solid: {
          _active: {
            bg: 'transparent',
          },
          _hover: {
            bg: 'transparent',
          },
          _focusVisible: {
            boxShadow: 'none',
          },
        },
      },
    },
  },
  colors: {
    brand: {
      gray: { 200: '#F0F0F0', 300: '#E3E3E3', 400: '#A6AAAE', 500: '#2D2D2D' },
    },
  },
  fonts: {
    heading: `'Univers', sans-serif`,
    body: `'Univers', sans-serif`,
  },
  textStyles: {
    h1: {
      fontFamily: 'heading',
      fontSize: ['20px', null, '32px'],
      lineHeight: '39px',
    },
    h2: {
      fontFamily: 'heading',
      fontSize: ['20px', null, '24px'],
      lineHeight: ['24px', null, '30px'],
      letterSpacing: '0.2px',
    },
    h3: {
      fontFamily: 'heading',
      fontSize: '12px',
      lineHeight: '15px',
      letterSpacing: ['0.03em', null, '0.02em'],
      textTransform: 'uppercase',
    },
    body: {
      fontFamily: 'body',
      fontSize: '13px',
      lineHeight: '16px',
      letterSpacing: '0.02em',
    },
    italic: {
      fontFamily: 'body',
      fontStyle: 'italic',
    },
    menu: {
      fontFamily: 'heading',
      fontSize: '36px',
    },
  },
}

const theme2 = extendTheme(customTheme)

export default theme2
